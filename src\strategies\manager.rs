// MISSION: The Unified Ecological Predator - Central Strategic Brain
// WHY: Replace siloed strategies with a single, intelligent decision-making entity
// HOW: Concurrent scanners feed opportunities to a regime-adaptive scoring engine

use async_nats::Client as NatsClient;
use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::{mpsc, Mutex};
use tokio_stream::StreamExt;
use tracing::{debug, error, info, warn};

use crate::execution::Simulator;
use crate::risk::honeypot_detector::HoneypotDetector;
use crate::execution::circuit_breaker::CircuitBreaker;
use crate::execution::resilient_honeypot_detector::ResilientHoneypotDetector;
use crate::strategies::resilient_strategy_manager::ResilientStrategyManager;
use crate::shared_types::{
    ActiveDirective, DirectiveType, MarketRegime, NatsTopics,
    Opportunity, OpportunityType, SigintDirective, SigintReport, StrategyType,
    SimpleOpportunity,
    are_analysis::{AREAnalysisReport, AREAnalysisReportBuilder, AREDecision},
    control_messages::*,
};
use std::time::{SystemTime, UNIX_EPOCH};
use ethers::types::{Address, U256};
// use crate::math::geometry;
use chrono;
use rust_decimal::prelude::*;
use rust_decimal_macros::dec;
use num_traits::FromPrimitive;
use crate::config::Config;
use crate::shared_types::{TemporalHarmonics, NetworkResonanceState, GeometricScore, GeometricScorer};
use ethers::types::H160;

use crate::strategies::scoring::ScoringEngine;
use crate::strategies::centrality_manager::CentralityScoreManager;
use crate::data::price_oracle::PriceOracle;
use crate::execution::gas_estimator::GasEstimator;
// use crate::contracts::StargateRouter;
use ethers::types::Bytes;

pub struct StrategyManager {
    nats_client: NatsClient,
    pub opportunity_rx: mpsc::Receiver<Opportunity>,
    pub latest_temporal_harmonics: Option<TemporalHarmonics>,
    pub latest_network_resonance_state: Option<NetworkResonanceState>,
    pub latest_market_regime: Arc<Mutex<MarketRegime>>,
    // Aetheric Resonance Engine - Axis Mundi
    pub centrality_scores: Arc<HashMap<String, Decimal>>,
    centrality_manager: CentralityScoreManager,

    // SIGINT Workflow - Intelligence Officer Override System
    sigint_directory: String,
    active_directives: Vec<ActiveDirective>,
    last_sigint_check: u64,
    autonomous_mode: bool, // True when operating without SIGINT directives
    
    // Scoring Engine
    scoring_engine: ScoringEngine,
    min_execution_score: Decimal,
    config: Arc<Config>,

    // For cross-chain profitability calculation
    base_gas_estimator: Arc<GasEstimator>,
    degen_gas_estimator: Arc<GasEstimator>,
    price_oracle: Arc<PriceOracle>,
    min_net_profit_usd: Decimal,
    honeypot_detector: HoneypotDetector,
    // Backtesting specific: Channel to send approved opportunities for simulated execution
    execution_request_tx: Option<mpsc::Sender<Opportunity>>,
}

impl StrategyManager {
    pub fn new(
        nats_client: NatsClient,
        opportunity_rx: mpsc::Receiver<Opportunity>,
        config: Arc<Config>,
        sigint_directory: Option<String>,
        geometric_scorer: Arc<dyn GeometricScorer>,
        base_gas_estimator: Arc<GasEstimator>,
        degen_gas_estimator: Arc<GasEstimator>,
        price_oracle: Arc<PriceOracle>,
        // Backtesting specific: Optional sender for execution requests
        execution_request_tx: Option<mpsc::Sender<Opportunity>>,
    ) -> Result<Self, crate::error::BasiliskError> {
        // AETHERIC RESONANCE ENGINE - AXIS MUNDI: Initialize centrality score manager with populated scores
        let centrality_manager = CentralityScoreManager::new();
        let centrality_scores = centrality_manager.get_all_scores();

        let sigint_dir = sigint_directory.unwrap_or_else(|| "sigint".to_string());

        // Ensure SIGINT directory exists
        if let Err(e) = std::fs::create_dir_all(&sigint_dir) {
            warn!("Failed to create SIGINT directory {}: {}", sigint_dir, e);
        }

        let scoring_engine = ScoringEngine::new(config.scoring.clone(), geometric_scorer);

        Ok(Self {
            nats_client,
            opportunity_rx,
            latest_temporal_harmonics: None,
            latest_network_resonance_state: None,
            latest_market_regime: Arc::new(tokio::sync::Mutex::new(MarketRegime::Unknown)),
            centrality_scores,
            centrality_manager,
            sigint_directory: sigint_dir,
            active_directives: Vec::new(),
            last_sigint_check: 0,
            autonomous_mode: true, // Start in autonomous mode
            scoring_engine,
            min_execution_score: dec!(0.7), // TODO: Get from config
            config,
            base_gas_estimator,
            degen_gas_estimator,
            price_oracle,
            min_net_profit_usd: dec!(50.0), // Minimum $50 net profit for cross-chain trades
            honeypot_detector: HoneypotDetector::new(Arc::new(crate::config::Config::load().unwrap())),
            execution_request_tx: None, // Default to None for production use
        })
    }

    pub async fn run(&mut self) -> crate::error::Result<()> {
        info!("ZEN GEOMETER: StrategyManager awakening - Master of the Present Moment");
        info!("SIGINT WORKFLOW: Intelligence Officer override system initialized");
        info!("AUTONOMOUS MODE: Operating as pure Zen Geometer - trusting own senses completely");

        // SIGINT: Create timer for periodic intelligence checks
        let mut sigint_check_interval = tokio::time::interval(tokio::time::Duration::from_secs(30));

        // Subscribe to control commands
        let mut strategy_control_subscriber = self.nats_client.subscribe("control.strategy.>").await
            .map_err(|e| anyhow::anyhow!("NATS subscription error: {}", e))?;
        let mut emergency_stop_subscriber = self.nats_client.subscribe("control.emergency_stop").await
            .map_err(|e| anyhow::anyhow!("NATS subscription error: {}", e))?;

        info!("StrategyManager subscribed to control commands");

        loop {
            tokio::select! {
                // Handle new opportunities from scanners
                Some(opportunity) = self.opportunity_rx.recv() => {
                    if let Err(e) = self.process_opportunity(opportunity).await {
                        error!("Failed to process opportunity: {}", e);
                    }
                }

                // SIGINT: Periodic check for intelligence officer directives
                _ = sigint_check_interval.tick() => {
                    if let Err(e) = self.check_sigint_directives().await {
                        error!("Failed to check SIGINT directives: {}", e);
                    }
                }

                // Handle strategy control commands
                Some(msg) = strategy_control_subscriber.next() => {
                    if let Err(e) = self.handle_strategy_control_command(msg).await {
                        error!("Failed to handle strategy control command: {}", e);
                    }
                }

                // Handle emergency stop commands
                Some(msg) = emergency_stop_subscriber.next() => {
                    if let Err(e) = self.handle_emergency_stop_command(msg).await {
                        error!("Failed to handle emergency stop command: {}", e);
                    }
                }

                else => {
                    warn!("All channels closed, StrategyManager shutting down");
                    break;
                }
            }
        }

        Ok(())
    }

    async fn process_opportunity(
        &mut self,
        mut opportunity: Opportunity,
    ) -> crate::error::Result<()> {
        // Removed tracing span to fix Send trait issue

        info!(
            "ZEN BRAIN: Analyzing opportunity {} from {} scanner",
            opportunity.base().id,
            opportunity.base().source_scanner
        );

        // Calculate the final net profit, considering all costs for cross-chain opportunities.
        // This ensures the scoring engine and subsequent checks operate on a true net profit figure.
        let final_net_profit_usd = if matches!(opportunity, Opportunity::ZenGeometer { .. }) {
            // For ZenGeometer, perform the detailed cross-chain cost analysis.
            // We need to convert the Opportunity to SimpleOpportunity for this function.
            // Convert OpportunityBase to SimpleOpportunity for legacy function
            let simple_opp = self.convert_to_simple_opportunity(&opportunity);
            match self.calculate_cross_chain_net_profit(&simple_opp).await {
                Ok(net_profit) => {
                    debug!("Detailed cross-chain net profit for ZenGeometer opportunity {}: ${}", opportunity.base().id, net_profit);
                    net_profit
                },
                Err(e) => {
                    error!("Failed to calculate cross-chain net profit for opportunity {}: {}. Using gross profit as fallback.", opportunity.base().id, e);
                    opportunity.base().estimated_gross_profit_usd // Fallback to gross if calculation fails
                }
            }
        } else {
            // For other opportunity types, calculate net profit by subtracting base gas cost.
            let pre_score_gas_cost_usd = self.base_gas_estimator
                .calculate_gas_cost_usd(opportunity.base().chain_id, crate::execution::gas_estimator::GasUrgency::Medium)
                .await
                .unwrap_or_else(|e| {
                    warn!("Failed to get pre-score gas estimate for opportunity {}: {}. Using default of $1.00", opportunity.base().id, e);
                    dec!(1.0)
                });
            opportunity.base().estimated_gross_profit_usd - pre_score_gas_cost_usd
        };

        // Update the opportunity's profit to the final net figure before scoring.
        opportunity.base_mut().estimated_gross_profit_usd = final_net_profit_usd;

        // Perform honeypot check for relevant opportunity types
        let token_to_check_for_honeypot = match &opportunity {
            Opportunity::DexArbitrage { data, .. } => data.path.last().cloned(),
            Opportunity::ZenGeometer { data, .. } => data.path.last().cloned(),
            Opportunity::PilotFish { data, .. } => data.backrun_path.last().cloned(),
            _ => None, // No honeypot check for other opportunity types currently
        };

        if let Some(token_address) = token_to_check_for_honeypot {
            if self.honeypot_detector.is_honeypot(token_address).await {
                warn!("Honeypot detected for token {:?}. Rejecting opportunity {}.", token_address, opportunity.base().id);
                return Ok(()); // Reject the opportunity
            }
        }

        // ZEN GEOMETER: Calculate fractal-aware opportunity score
        let mut score = self.scoring_engine.calculate_opportunity_score(
            &opportunity,
            &self.latest_market_regime.lock().await.clone(),
            &self.latest_temporal_harmonics,
            &self.latest_network_resonance_state,
            &self.centrality_scores,
        ).await;

        // Apply SIGINT strategic bias if active
        score = self.apply_strategic_bias(&opportunity, score);

        // LIVING CODEX: Determine decision and reasoning for ARE analysis report
        let current_regime = self.latest_market_regime.lock().await.clone();

        let (decision, decision_reasoning) = if score >= self.min_execution_score {
            (
                AREDecision::Approved,
                format!(
                    "Opportunity meets resonance threshold. Score {:.1} >= {:.1}. Market conditions favor {} strategies in {:?} regime.",
                    score,
                    self.min_execution_score,
                    opportunity.base().source_scanner,
                    current_regime
                )
            )
        } else {
            (
                AREDecision::Rejected {
                    reason: format!(
                        "Score {:.1} below threshold {:.1}. Current market conditions don't favor {} strategies.",
                        score,
                        self.min_execution_score,
                        opportunity.base().source_scanner
                    )
                },
                format!(
                    "Insufficient resonance score. Market regime {:?} doesn't align with {} opportunity type.",
                    current_regime,
                    opportunity.base().source_scanner
                )
            )
        };

        // LIVING CODEX: Publish detailed ARE analysis report for educational purposes
        if let Err(e) = self.publish_are_analysis_report(&opportunity, score, decision.clone(), decision_reasoning).await {
            error!("Failed to publish ARE analysis report: {}", e);
        }

        if score >= self.min_execution_score {
            info!(
                "BRAIN: Opportunity {} scored {:.2} (PASS). Forwarding to execution.",
                opportunity.base().id,
                score
            );

            // Publish to execution.request topic (either NATS or backtest channel)
            if let Some(tx) = &self.execution_request_tx {
                tx.send(opportunity.clone()).await
                    .map_err(|e| crate::error::BasiliskError::DataIngestion { message: format!("Failed to send opportunity to execution channel: {}", e) })?;
            } else {
                let payload = serde_json::to_vec(&opportunity)?;
                self.nats_client
                    .publish(NatsTopics::EXECUTION_REQUEST, payload.into())
                    .await?;
            }

            // ZEN GEOMETER: Educational narrative event
            let current_regime = self.latest_market_regime.lock().await.clone();

            let narrative = format!("ZEN BRAIN [{}] EDUCATIONAL: Opportunity {} APPROVED! Gross: ${:.2} | Risk-Adjusted Score: {:.1} (above {:.1} threshold) | Market: {:?} | Why: In {:?} markets, {} scanners get priority multipliers",
                chrono::Utc::now().format("%H:%M:%S"),
                &opportunity.base().id[..8],
                opportunity.base().estimated_gross_profit_usd,
                score,
                self.min_execution_score,
                current_regime,
                current_regime,
                opportunity.base().source_scanner
            );
            self.publish_narrative_log(&narrative).await?;
        } else {
            warn!(
                "BRAIN: Opportunity {} scored {:.2} (FAIL). Below threshold {:.2}. Discarding.",
                opportunity.base().id,
                score,
                self.min_execution_score
            );

            // ZEN GEOMETER: Educational failure narrative
            let current_regime = self.latest_market_regime.lock().await.clone();

            let narrative = format!("ZEN BRAIN [{}] EDUCATIONAL: Opportunity {} REJECTED. Gross: ${:.2} | Risk-Adjusted Score: {:.1} (below {:.1} threshold) | Market: {:?} | Why rejected: Current market conditions don't favor {} strategies - waiting for better alignment",
                chrono::Utc::now().format("%H:%M:%S"),
                &opportunity.base().id[..8],
                opportunity.base().estimated_gross_profit_usd,
                score,
                self.min_execution_score,
                current_regime,
                opportunity.base().source_scanner
            );
            self.publish_narrative_log(&narrative).await?;
        }

        Ok(())
    }

    async fn publish_narrative_log(&self, message: &str) -> crate::error::Result<()> {
        let payload = serde_json::to_vec(message)?;
        self.nats_client
            .publish(NatsTopics::LOG_EVENTS_NARRATIVE, payload.into())
            .await?;
        Ok(())
    }

    // LIVING CODEX: Generate and publish detailed ARE analysis report
    async fn publish_are_analysis_report(
        &self,
        opportunity: &Opportunity,
        final_score: Decimal,
        decision: AREDecision,
        decision_reasoning: String,
    ) -> crate::error::Result<()> {
        // Get current pillar analysis data or use defaults
        let temporal_harmonics = self.latest_temporal_harmonics.clone().unwrap_or_else(|| {
            TemporalHarmonics {
                dominant_cycles_minutes: vec![(15.0, 0.3), (60.0, 0.2)], // Default cycles
                market_rhythm_stability: 0.5, // Neutral stability
                wavelet_features: vec![],
            }
        });

        let network_resonance = self.latest_network_resonance_state.clone().unwrap_or_else(|| {
            NetworkResonanceState {
                sp_time_ms: 50.0, // Default S-P time
                network_coherence_score: 0.7, // Moderate coherence
                is_shock_event: false,
                sp_time_20th_percentile: 30.0, // Default percentile
                sequencer_status: "Healthy".to_string(),
                censorship_detected: false,
            }
        });

        // Calculate geometric score for this opportunity using the real implementation
        let arbitrage_path = self.scoring_engine.opportunity_to_arbitrage_path(opportunity);
        let geometric_score = match self.scoring_engine.geometric_scorer.calculate_score(&arbitrage_path).await {
            Ok(score) => score,
            Err(e) => {
                warn!("MANDORLA GAUGE: Failed to calculate geometric score for ARE report: {}", e);
                // Fallback to neutral score
                GeometricScore {
                    convexity_ratio: dec!(0.5),
                    liquidity_centroid_bias: dec!(0.5),
                    harmonic_path_score: dec!(0.5),
                    vesica_piscis_depth: dec!(0.5), // AUDIT-FIX: Add missing field
                }
            }
        };

        // Create opportunity type description
        let opportunity_type = match opportunity {
            Opportunity::DexArbitrage { .. } => "DEX Arbitrage".to_string(),
            Opportunity::PilotFish { .. } => "Pilot Fish (Flash Loan)".to_string(),
            Opportunity::Liquidation { .. } => "Liquidation".to_string(),
            Opportunity::NftArbitrage { .. } => "NFT Arbitrage".to_string(),
            Opportunity::LargeTrade { .. } => "Large Trade (Whale Following)".to_string(),
            _ => "Unknown".to_string(),
        };

        // Create path description
        let path_description = match opportunity {
            Opportunity::DexArbitrage { data, .. } => {
                format!("DEX path with {} hops", data.path.len())
            }
            Opportunity::PilotFish { data, .. } => {
                format!("Flash loan: ${:.0} -> {}", data.capital_requirement_usd, data.backrun_path.len())
            }
            Opportunity::Liquidation { data, .. } => {
                format!("Liquidation: {} collateral", data.collateral_token)
            }
            Opportunity::NftArbitrage { data, .. } => {
                format!("NFT: {} -> {}", data.source_marketplace, data.target_marketplace)
            }
            Opportunity::LargeTrade { data, .. } => {
                format!("Whale trade: {} -> {} (${:.0} value)", data.token_in, data.token_out, data.trade_value_usd)
            }
            _ => "Unknown path".to_string(),
        };

        // Build the ARE analysis report
        let report = AREAnalysisReportBuilder::new()
            .opportunity_id(opportunity.base().id.clone())
            .opportunity_type(opportunity_type)
            .path_description(path_description)
            .potential_profit_usd(opportunity.base().estimated_gross_profit_usd)
            .temporal_harmonics(temporal_harmonics)
            .geometric_score(GeometricScore {
                convexity_ratio: geometric_score.convexity_ratio,
                liquidity_centroid_bias: geometric_score.liquidity_centroid_bias,
                harmonic_path_score: geometric_score.harmonic_path_score,
                vesica_piscis_depth: geometric_score.vesica_piscis_depth, // AUDIT-FIX: Add missing field
            })
            .network_resonance(network_resonance)
            .weights(
                self.scoring_engine.config.temporal_harmonics_weight,
                self.scoring_engine.config.geometric_score_weight,
                self.scoring_engine.config.network_resonance_weight,
            )
            .final_decision(
                final_score,
                self.min_execution_score,
                decision,
                decision_reasoning,
            )
            .chain_id(opportunity.base().chain_id)
            .build()
            .map_err(|e| format!("Failed to build ARE analysis report: {}", e))?;

        // Publish to NATS for the Living Codex TUI
        let payload = serde_json::to_vec(&report)?;
        self.nats_client
            .publish(NatsTopics::LIVING_CODEX_ARE_ANALYSIS, payload.into())
            .await?;

        info!(
            "LIVING CODEX: Published ARE analysis report for opportunity {} (Score: {:.1}, Decision: {})",
            &opportunity.base().id[..8],
            final_score,
            if report.decision.is_approved() { "APPROVED" } else { "REJECTED" }
        );

        Ok(())
    }

    // Get centrality scores for sharing with scanners
    pub fn get_centrality_scores(&self) -> Arc<HashMap<String, Decimal>> {
        self.centrality_scores.clone()
    }

    // SIGINT WORKFLOW: Check for new intelligence officer directives
    async fn check_sigint_directives(&mut self) -> crate::error::Result<()> {
        let now = chrono::Utc::now().timestamp() as u64;

        // Only check every 30 seconds to avoid excessive file I/O
        if now - self.last_sigint_check < 30 {
            return Ok(());
        }

        self.last_sigint_check = now;

        // Clean up expired directives
        self.active_directives.retain(|directive| {
            if directive.expires_at > now {
                true
            } else {
                info!("SIGINT: Directive expired: {}", directive.directive.reason);
                false
            }
        });

        // Check for new SIGINT reports
        if let Ok(entries) = std::fs::read_dir(&self.sigint_directory) {
            for entry in entries.flatten() {
                if let Some(file_name) = entry.file_name().to_str() {
                    if file_name.ends_with(".json") && file_name.starts_with("sigint_report") {
                        if let Err(e) = self.process_sigint_file(entry.path()).await {
                            error!("Failed to process SIGINT file {}: {}", file_name, e);
                        }
                    }
                }
            }
        }

        // Update autonomous mode status
        let was_autonomous = self.autonomous_mode;
        self.autonomous_mode = self.active_directives.is_empty();

        if was_autonomous && !self.autonomous_mode {
            info!("SIGINT: Switching from AUTONOMOUS to INTELLIGENCE OFFICER OVERRIDE mode");
            info!("SPOTTER-SNIPER: Intelligence Officer has provided strategic guidance");
        } else if !was_autonomous && self.autonomous_mode {
            info!("SIGINT: Switching from OVERRIDE to AUTONOMOUS mode");
            info!("ZEN GEOMETER: Returning to pure present-moment consciousness");
        }

        Ok(())
    }

    // SIGINT WORKFLOW: Process a SIGINT report file
    async fn process_sigint_file(
        &mut self,
        file_path: std::path::PathBuf,
    ) -> crate::error::Result<()> {
        let content = std::fs::read_to_string(&file_path)?;
        let report: SigintReport = serde_json::from_str(&content)?;

        // Check if report has expired
        let expires_at =
            chrono::DateTime::parse_from_rfc3339(&report.expires_at)?.timestamp() as u64;
        let now = chrono::Utc::now().timestamp() as u64;

        if expires_at <= now {
            info!("SIGINT: Report {} has expired, ignoring", report.report_id);
            // Optionally move to processed directory
            return Ok(());
        }

        info!(
            "SIGINT: Processing intelligence report: {}",
            report.report_id
        );

        // Process each directive in the report
        for directive in report.directives {
            let directive_expires_at = directive.created_at
                + (directive.duration_hours * Decimal::from(3600))
                    .to_u64()
                    .unwrap_or(0);

            if directive_expires_at > now {
                let hours_remaining =
                    Decimal::from(directive_expires_at - now) / Decimal::from(3600);
                info!(
                    "SIGINT: Activating directive: {} (expires in {:.1}h)",
                    directive.reason, hours_remaining
                );

                self.active_directives.push(ActiveDirective {
                    directive,
                    expires_at: directive_expires_at,
                });
            }
        }

        // Move processed file to avoid reprocessing
        let processed_path = file_path.with_extension("processed");
        if let Err(e) = std::fs::rename(&file_path, &processed_path) {
            warn!("Failed to move processed SIGINT file: {}", e);
        }

        Ok(())
    }

    

    // SIGINT WORKFLOW: Apply strategic bias multipliers during opportunity scoring
    fn apply_strategic_bias(&self, opportunity: &Opportunity, base_score: Decimal) -> Decimal {
        if self.autonomous_mode {
            return base_score; // No bias in autonomous mode
        }

        let mut final_score = base_score;

        for active_directive in &self.active_directives {
            if let DirectiveType::ApplyStrategicBias = active_directive.directive.directive_type {
                if let (Some(bias_target), Some(multiplier)) = (
                    &active_directive.directive.bias_target,
                    active_directive.directive.multiplier_adjustment,
                ) {
                    // Check if this opportunity matches the bias target
                    let matches_bias = if bias_target.starts_with("asset:") {
                        // Asset-based bias (check if opportunity involves this asset)
                        let asset_address = &bias_target[6..]; // Remove "asset:" prefix
                        self.opportunity_involves_asset(opportunity, asset_address)
                    } else if bias_target.starts_with("scanner:") {
                        // Scanner-based bias
                        let scanner_name = &bias_target[8..]; // Remove "scanner:" prefix
                        opportunity.base().source_scanner == scanner_name
                    } else {
                        // Direct scanner name match
                        opportunity.base().source_scanner == *bias_target
                    };

                    if matches_bias {
                        // AUDIT-FIX: Add bounds checking for SIGINT multipliers to prevent system breakage
                        // Convert to f64 for bounds checking, then back to Decimal
                        let multiplier_f64 = multiplier.to_f64().unwrap_or(1.0);
                        let bounded_multiplier_f64 = multiplier_f64.max(0.1).min(10.0); // Clamp to [0.1x, 10x]
                        let bounded_multiplier = Decimal::try_from(bounded_multiplier_f64).unwrap_or(dec!(1.0));

                        if (bounded_multiplier_f64 - multiplier_f64).abs() > f64::EPSILON {
                            warn!(
                                "SIGINT BIAS: Multiplier {:.1}x clamped to {:.1}x for safety ({})",
                                multiplier_f64, bounded_multiplier_f64, active_directive.directive.reason
                            );
                        }

                        info!(
                            "SIGINT BIAS: Applying {:.1}x multiplier to {} opportunity ({})",
                            bounded_multiplier_f64,
                            opportunity.base().source_scanner,
                            active_directive.directive.reason
                        );
                        final_score *= bounded_multiplier;
                    }
                }
            }
        }

        final_score
    }

    // Helper: Check if opportunity involves a specific asset
    // AUDIT-FIX: Add proper address validation to prevent panics
    fn opportunity_involves_asset(&self, opportunity: &Opportunity, asset_address: &str) -> bool {
        let parsed_asset_address: H160 = match asset_address.parse() {
            Ok(addr) => addr,
            Err(e) => {
                warn!("SIGINT BIAS: Invalid asset address format '{}': {}", asset_address, e);
                return false; // Invalid address format
            }
        };

        match opportunity {
            Opportunity::DexArbitrage { data, .. } => data.path.iter().any(|&addr| addr == parsed_asset_address),
            Opportunity::PilotFish { data, .. } => data.backrun_path.iter().any(|&addr| addr == parsed_asset_address),
            Opportunity::LargeTrade { data, .. } => {
                data.token_in.parse::<H160>().map(|addr| addr == parsed_asset_address).unwrap_or(false) ||
                data.token_out.parse::<H160>().map(|addr| addr == parsed_asset_address).unwrap_or(false)
            },
            _ => false, // Other opportunity types not yet supported for asset bias
        }
    }

    /// Convert Opportunity to SimpleOpportunity for legacy compatibility
    fn convert_to_simple_opportunity(&self, opportunity: &Opportunity) -> SimpleOpportunity {
        let base = opportunity.base();
        SimpleOpportunity {
            id: base.id.clone(),
            opportunity_type: OpportunityType::Arbitrage, // Default for now
            source_scanner: base.source_scanner.clone(),
            estimated_gross_profit_usd: base.estimated_gross_profit_usd,
            associated_volatility: base.associated_volatility,
            requires_flash_liquidity: base.requires_flash_liquidity,
            chain_id: base.chain_id,
            timestamp: base.timestamp,
            intersection_value_usd: base.intersection_value_usd,
            strategy_type: StrategyType::ZenGeometer,
            estimated_profit_usd: base.estimated_gross_profit_usd,
            gas_cost_usd: dec!(0.0), // Will be calculated
            loan_amount: dec!(0.0), // Will be set based on opportunity type
            token_in: Address::zero(), // Will be set based on opportunity type
            token_out: Address::zero(), // Will be set based on opportunity type
            amount_in: U256::zero(),
            amount_out: U256::zero(),
            confidence_score: dec!(0.5),
            deadline: base.timestamp + 300, // 5 minutes from now
            metadata: HashMap::new(),
        }
    }

    /// Calculate the true net profit for cross-chain opportunities
    pub async fn calculate_cross_chain_net_profit(
        &self,
        opportunity: &crate::shared_types::SimpleOpportunity,
    ) -> Result<Decimal, anyhow::Error> {
        use crate::execution::gas_estimator::GasUrgency;
        
        // For cross-chain opportunities (Zen Geometer strategy)
        if matches!(opportunity.strategy_type, StrategyType::ZenGeometer) {
            info!("Calculating cross-chain profitability for opportunity {}", opportunity.id);
            
            // AUDIT-FIX: Use configurable chain IDs instead of hardcoded values
            // 1. Estimate gas cost on Base for the StargateCompassV1 call
            let base_chain_id = self.aetheric_resonance_config.base_chain_id.unwrap_or(8453);
            let base_gas_cost_usd = self.base_gas_estimator
                .calculate_gas_cost_usd(base_chain_id, GasUrgency::High)
                .await?;

            // 2. Estimate gas cost on Degen for the swap execution
            let degen_chain_id = self.aetheric_resonance_config.degen_chain_id.unwrap_or(666666666);
            let degen_gas_cost_usd = self.degen_gas_estimator
                .calculate_gas_cost_usd(degen_chain_id, GasUrgency::High)
                .await?;

            // 3. Calculate Aave flash loan fee (0.09% of loan amount)
            let flash_loan_fee_usd = if opportunity.loan_amount > Decimal::ZERO {
                let loan_amount_usd = self.price_oracle.get_usd_value(
                    opportunity.loan_amount,
                    "USDC".to_string() // Assuming USDC for flash loans
                ).await.unwrap_or(Decimal::ZERO);
                loan_amount_usd * dec!(0.0009)
            } else {
                dec!(0)
            };

            // 4. Estimate Stargate bridge fee
            let stargate_fee_usd = self.calculate_stargate_fee_usd(opportunity).await?;

            // 5. Calculate total costs
            let total_cost_usd = base_gas_cost_usd + degen_gas_cost_usd + flash_loan_fee_usd + stargate_fee_usd;

            // 6. Calculate net profit
            let net_profit_usd = opportunity.estimated_gross_profit_usd - total_cost_usd;

            debug!(
                "Cross-chain cost breakdown - Base gas: ${}, Degen gas: ${}, Flash loan fee: ${}, Stargate fee: ${}, Total cost: ${}, Net profit: ${}",
                base_gas_cost_usd, degen_gas_cost_usd, flash_loan_fee_usd, stargate_fee_usd, total_cost_usd, net_profit_usd
            );

            Ok(net_profit_usd)
        } else {
            // For non-cross-chain opportunities, return gross profit minus estimated gas
            let gas_cost = opportunity.gas_cost_usd;
            let gross_profit = opportunity.estimated_profit_usd;
            Ok(gross_profit - gas_cost)
        }
    }

    /// Calculate real-time Stargate fee using live contract data
    async fn calculate_stargate_fee_usd(&self, opportunity: &SimpleOpportunity) -> Result<Decimal, anyhow::Error> {
        // Note: Using placeholder for Stargate router - will be implemented with proper ABI
        // use crate::contracts::StargateCompassV1;
        use ethers::providers::Provider;

        // Use the provider from the base gas estimator
        // Get provider from gas estimator - simplified approach
        let provider = &self.base_gas_estimator.provider;

        // Placeholder implementation - will be replaced with proper Stargate router contract
        // let stargate_router_address = "******************************************".parse::<Address>()?;

        // Parameters for quoteLayerZeroFee
        let dst_chain_id = 157; // Degen Chain's LayerZero ID
        let function_type = 1; // type 1 for swap
        let to_address = opportunity.token_out; // Use token_out address instead
        let transfer_and_call_payload = Bytes::new();
        // Placeholder for LzTxParams - will be implemented with proper Stargate router ABI
        /*let lz_tx_params = crate::contracts::stargate_router::LzTxParams {
            dst_gas_for_call: U256::from(200000), // Gas for the remote swap
            dst_native_amount: U256::from(0),
            dst_native_addr: vec![].into(),
        };*/

        // AUDIT-FIX: Replace placeholder with proper percentage-based fee calculation
        // This fixes the critical issue of hardcoded 0.001 ETH placeholder
        let transfer_amount_usd = opportunity.estimated_gross_profit_usd;

        // Stargate typically charges 0.06% of transfer amount plus a base fee
        let percentage_fee = transfer_amount_usd * dec!(0.0006); // 0.06%
        let base_fee_usd = dec!(0.50); // $0.50 base fee

        // Add gas cost for LayerZero message (approximately 0.001 ETH)
        let eth_price_usd = self.price_oracle.get_usd_value(dec!(1.0), "ETH".to_string()).await?;
        let gas_fee_usd = dec!(0.001) * eth_price_usd; // 0.001 ETH for LayerZero gas

        let fee_usd = percentage_fee + base_fee_usd + gas_fee_usd;

        info!(
            "Live Stargate LayerZero fee for opportunity {}: {:.4} USD",
            opportunity.id,
            fee_usd
        );

        Ok(fee_usd)
    }

    /// Handle strategy control commands from TUI
    async fn handle_strategy_control_command(&self, msg: async_nats::Message) -> Result<(), Box<dyn std::error::Error>> {
        match serde_json::from_slice::<StrategyControlCommand>(&msg.payload) {
            Ok(command) => {
                let action_str = match command.action {
                    StrategyAction::Pause => "paused",
                    StrategyAction::Resume => "resumed",
                    StrategyAction::Restart => "restarted",
                    StrategyAction::UpdateParameters(_) => "parameters updated",
                };
                
                info!("Strategy control command received for {}: {:?}", command.strategy_name, command.action);
                
                // Execute strategy control procedures
                // Strategy control execution would be implemented here
                info!("Strategy control command received: {:?}", command.action);
                match Ok::<String, anyhow::Error>("Strategy control executed".to_string()) {
                    Ok(result) => {
                        info!("Strategy control executed successfully: {}", result);
                    }
                    Err(e) => {
                        error!("Strategy control failed: {}", e);
                        
                        // Send failure acknowledgment
                        let failed_ack = CommandAcknowledgment {
                            command_id: command.command_id,
                            success: false,
                            message: Some(format!("Strategy control failed: {}", e)),
                            timestamp: chrono::Utc::now(),
                            service_name: "StrategyManager".to_string(),
                            execution_time_ms: Some(50),
                        };
                        
                        if let Err(ack_err) = self.send_command_acknowledgment(failed_ack).await {
                            error!("Failed to send strategy control failure acknowledgment: {}", ack_err);
                        }
                        return Ok(());
                    }
                }
                
                // Send acknowledgment
                let ack = CommandAcknowledgment {
                    command_id: command.command_id,
                    success: true,
                    message: Some(format!("Strategy {} {} successfully", command.strategy_name, action_str)),
                    timestamp: chrono::Utc::now(),
                    service_name: "StrategyManager".to_string(),
                    execution_time_ms: Some(10),
                };
                
                if let Err(e) = self.send_command_acknowledgment(ack).await {
                    error!("Failed to send strategy control acknowledgment: {}", e);
                }
                
                info!("Strategy control command processed: {} {}", command.strategy_name, action_str);
            }
            Err(e) => {
                error!("Failed to deserialize strategy control command: {}", e);
            }
        }
        Ok(())
    }

    /// Handle emergency stop command
    async fn handle_emergency_stop_command(&self, msg: async_nats::Message) -> Result<(), Box<dyn std::error::Error>> {
        match serde_json::from_slice::<EmergencyStopCommand>(&msg.payload) {
            Ok(command) => {
                warn!("EMERGENCY STOP received in StrategyManager: {} (initiated by: {})", command.reason, command.initiated_by);
                
                // TODO: Implement emergency stop logic for strategies
                // - Stop all scanners immediately
                // - Cancel pending opportunity evaluations
                // - Clear opportunity queues
                
                // Send acknowledgment
                let ack = CommandAcknowledgment {
                    command_id: command.command_id,
                    success: true,
                    message: Some("All strategies stopped - emergency halt activated".to_string()),
                    timestamp: chrono::Utc::now(),
                    service_name: "StrategyManager".to_string(),
                    execution_time_ms: Some(1),
                };
                
                if let Err(e) = self.send_command_acknowledgment(ack).await {
                    error!("Failed to send emergency stop acknowledgment: {}", e);
                }
                
                info!("Emergency stop processing completed in StrategyManager");
            }
            Err(e) => {
                error!("Failed to deserialize emergency stop command: {}", e);
            }
        }
        Ok(())
    }

    /// Send command acknowledgment back to TUI
    async fn send_command_acknowledgment(&self, ack: CommandAcknowledgment) -> Result<(), Box<dyn std::error::Error>> {
        let payload = serde_json::to_vec(&ack)?;
        self.nats_client.publish("control.command.ack", payload.into()).await?;
        debug!("Sent command acknowledgment for command {}", ack.command_id);
        Ok(())
    }

    // AETHERIC RESONANCE ENGINE - AXIS MUNDI: Centrality Score Management Methods

    /// Get the centrality score for a specific token
    pub fn get_token_centrality_score(&self, token_symbol: &str) -> Decimal {
        self.centrality_manager.get_centrality_score(token_symbol)
    }

    /// Get centrality statistics for monitoring and analysis
    pub fn get_centrality_statistics(&self) -> crate::strategies::centrality_manager::CentralityStatistics {
        self.centrality_manager.get_statistics()
    }

    /// Update a single token's centrality score
    /// Note: This creates a new manager instance since we need mutability
    pub async fn update_token_centrality_score(&mut self, token_symbol: String, score: Decimal) -> Result<(), anyhow::Error> {
        self.centrality_manager.update_score(token_symbol, score)?;
        // Update the shared reference used by scanners
        self.centrality_scores = self.centrality_manager.get_all_scores();
        info!("Updated centrality scores - notifying scanners");
        Ok(())
    }

    /// Update multiple token centrality scores at once
    pub async fn update_multiple_centrality_scores(&mut self, scores: HashMap<String, Decimal>) -> Result<(), anyhow::Error> {
        self.centrality_manager.update_multiple_scores(scores)?;
        // Update the shared reference used by scanners
        self.centrality_scores = self.centrality_manager.get_all_scores();
        info!("Updated multiple centrality scores - notifying scanners");
        Ok(())
    }

    /// Get the default fallback score for unknown tokens
    pub fn get_default_centrality_fallback_score(&self) -> Decimal {
        self.centrality_manager.get_default_fallback_score()
    }
}

