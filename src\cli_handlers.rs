use crate::config::Config;
use crate::error::{BasiliskError, Result};
use crate::logging::{TradingContext, ErrorCode};
use crate::{log_info, log_error, log_warning};
use ethers::providers::{Http, Provider, Middleware};
use crossterm::{
    execute, 
    terminal::{disable_raw_mode, LeaveAlternateScreen},
    event::DisableMouseCapture
};
use ratatui::{Terminal, backend::CrosstermBackend};
use tracing::{info, warn, error};

/// Simple system validation for minimal mode
pub async fn validate_system(config: &Config) -> Result<()> {
    let validation_context = TradingContext::new("SystemValidator", "validate_system");
    
    log_info!(validation_context, "Validating system configuration and connectivity");

    // 1. Validate NATS connection - TODO: Add NATS config to new Config struct
    // let nats_context = validation_context.clone();
    // log_info!(nats_context, "Validating NATS connection: {}", config.nats.url);
    // match async_nats::connect(&config.nats.url).await {
    //     Ok(_) => {
    //         log_info!(nats_context, "NATS connection successful");
    //     },
    //     Err(e) => {
    //         log_error!(nats_context, ErrorCode::ERpcConnectionFailed, 
    //             "NATS connection failed: {}", e);
    //         return Err(BasiliskError::DataIngestion { 
    //             message: format!("Could not connect to NATS at {}. Is the NATS server running? Error: {}", 
    //                 config.nats.url, e) 
    //         });
    //     }
    // }

    // 2. Validate RPC connection for each configured chain
    for (chain_id, chain_config) in &config.chains {
        let rpc_context = validation_context.clone();
        log_info!(rpc_context, "Validating RPC connection for chain {}: {}", chain_id, chain_config.rpc_url);
        match Provider::<Http>::try_from(&chain_config.rpc_url) {
            Ok(provider) => match provider.get_block_number().await {
                Ok(block_number) => {
                    log_info!(rpc_context, "RPC connection for chain {} successful, current block: {}", chain_id, block_number);
                },
                Err(e) => {
                    log_error!(rpc_context, ErrorCode::ERpcInvalidResponse, 
                        "RPC connection for chain {} established but failed to get block number: {}", chain_id, e);
                    return Err(BasiliskError::DataIngestion { 
                        message: format!("RPC connection for chain {} established but failed to get block number. Error: {}", chain_id, e) 
                    });
                }
            },
            Err(e) => {
                log_error!(rpc_context, ErrorCode::ERpcConnectionFailed, 
                    "RPC connection for chain {} failed: {}", chain_id, e);
                return Err(BasiliskError::DataIngestion { 
                    message: format!("Could not connect to RPC for chain {} at {}. Check the URL and network connectivity. Error: {}", 
                        chain_id, chain_config.rpc_url, e) 
                });
            }
        }
    }


    // 3. Skip database validation for minimal mode
    log_info!(validation_context, "Database validation: SKIPPED (running in minimal mode)");

    // 4. Skip Redis validation for minimal mode  
    log_info!(validation_context, "Redis validation: SKIPPED (running in minimal mode)");

    log_info!(validation_context, "System validation complete (minimal mode)");
    Ok(())
}

// Placeholder functions for missing imports
pub async fn handle_config_command(_config: &crate::config::Config, _command: &crate::cli::ConfigCommands) -> Result<()> {
    println!("Config command handling not implemented in minimal mode");
    Ok(())
}

pub async fn handle_utils_command(_config: &crate::config::Config, _command: &crate::cli::UtilsCommands) -> Result<()> {
    println!("Utils command handling not implemented in minimal mode");
    Ok(())
}

pub async fn handle_tui_command(config: std::sync::Arc<Config>) -> Result<()> {
    use crate::tui::app::App;
    use crossterm::{
        event::{self, DisableMouseCapture, EnableMouseCapture, Event, KeyCode},
        execute,
        terminal::{disable_raw_mode, enable_raw_mode, EnterAlternateScreen, LeaveAlternateScreen},
    };
    use ratatui::{backend::CrosstermBackend, Terminal};
    use std::io;
    use tokio::time::{interval, Duration};
    
    println!("ZEN GEOMETER: Initializing TUI interface...");
    
    // Connect to NATS - TODO: Add NATS config to new Config struct
    let nats_url = std::env::var("NATS_URL").unwrap_or_else(|_| "nats://localhost:4222".to_string());
    let client = async_nats::connect(&nats_url).await
        .map_err(|e| BasiliskError::DataIngestion { 
            message: format!("Failed to connect to NATS: {}", e) 
        })?;
    
    // Setup terminal for TUI
    enable_raw_mode().map_err(|e| BasiliskError::DataIngestion { 
        message: format!("Failed to enable raw mode: {}", e) 
    })?;
    let mut stdout = io::stdout();
    execute!(stdout, EnterAlternateScreen, EnableMouseCapture)
        .map_err(|e| BasiliskError::DataIngestion { 
            message: format!("Failed to setup terminal: {}", e) 
        })?;
    let backend = CrosstermBackend::new(stdout);
    let mut terminal = Terminal::new(backend)
        .map_err(|e| BasiliskError::DataIngestion { 
            message: format!("Failed to create terminal: {}", e) 
        })?;

    // Create TUI app
    let mut app = App::new_harness((*config).clone(), client);
    
    // Initialize app
    if let Err(e) = app.init().await {
        cleanup_terminal(&mut terminal)?;
        return Err(BasiliskError::DataIngestion { 
            message: format!("Failed to initialize TUI app: {}", e) 
        });
    }

    println!("TUI initialized successfully. Use TAB to navigate, Q to quit.");

    // Main TUI event loop
    let mut last_tick = std::time::Instant::now();
    let tick_rate = Duration::from_millis(100);

    while app.running {
        // Draw UI
        if let Err(e) = terminal.draw(|f| app.render(f)) {
            eprintln!("Failed to draw TUI: {}", e);
            break;
        }

        // Handle input events
        let timeout = tick_rate
            .checked_sub(last_tick.elapsed())
            .unwrap_or_else(|| Duration::from_secs(0));

        if event::poll(timeout).map_err(|e| BasiliskError::DataIngestion { 
            message: format!("Failed to poll events: {}", e) 
        })? {
            if let Event::Key(key) = event::read().map_err(|e| BasiliskError::DataIngestion { 
                message: format!("Failed to read event: {}", e) 
            })? {
                app.handle_key(key);
            }
        }

        // Update app state periodically
        if last_tick.elapsed() >= tick_rate {
            if let Err(e) = app.update().await {
                eprintln!("Failed to update TUI state: {}", e);
            }
            last_tick = std::time::Instant::now();
        }
    }

    // Cleanup and restore terminal
    cleanup_terminal(&mut terminal)?;
    println!("TUI shutdown complete");

    Ok(())
}

fn cleanup_terminal(terminal: &mut Terminal<CrosstermBackend<std::io::Stdout>>) -> Result<()> {
    disable_raw_mode().map_err(|e| BasiliskError::DataIngestion { 
        message: format!("Failed to disable raw mode: {}", e) 
    })?;
    execute!(
        terminal.backend_mut(),
        LeaveAlternateScreen,
        DisableMouseCapture
    ).map_err(|e| BasiliskError::DataIngestion { 
        message: format!("Failed to cleanup terminal: {}", e) 
    })?;
    terminal.show_cursor().map_err(|e| BasiliskError::DataIngestion { 
        message: format!("Failed to show cursor: {}", e) 
    })?;
    Ok(())
}
