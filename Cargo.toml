[package]
name = "basilisk_bot"
version = "0.1.0"
edition = "2021"
default-run = "basilisk_bot"

[lints.rust]
# Ignore common warnings
unused_variables = "allow"
unused_imports = "allow"
dead_code = "allow"
unused_mut = "allow"
unused_assignments = "allow"
unreachable_code = "allow"
unused_must_use = "allow"
# Or use "warn" instead of "allow" to show but not fail

[dependencies]
# Core Async Runtime & Utilities
tokio = { version = "1", features = ["full"] }
tokio-tungstenite = { version = "0.21", features = ["rustls-tls-webpki-roots"] }
futures-util = "0.3"
futures = "0.3"

# Command-Line Interface
clap = { version = "4", features = ["derive"] }
dialoguer = "0.11"

# Configuration Management
config = { version = "0.13", features = ["toml"] }
dotenvy = "0.15"

# Data Serialization
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
serde_yaml = "0.9"

# Error Handling
thiserror = "1.0"
anyhow = "1.0"
eyre = "0.6"
async-trait = "0.1"

# Blockchain Interaction (EVM)
ethers = { version = "2.0", features = ["ws", "rustls", "abigen"] }

# Database & Caching
sqlx = { version = "0.8", features = ["runtime-tokio-rustls", "postgres", "time"] }
redis = { version = "0.23", features = ["tokio-comp"] }

# Messaging
async-nats = "0.33"
tokio-stream = "0.1"

# High-Performance Logging
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["env-filter", "json"] }

# Utilities
rand = "0.8"
uuid = { version = "1.3", features = ["v4", "serde"] }
hex = "0.4"
chrono = { version = "0.4", features = ["serde"] }
rustfft = "6.1.0"
url = "2.4"
toml = "0.8"

# Graph Theory for Pathfinding
petgraph = "0.6"

# PageRank for Axis Mundi Heuristic
page_rank = "0.2"

# High-Precision Math
rust_decimal = "1.32"
rust_decimal_macros = "1.32"
num-traits = "0.2"
ethnum = "1"

# Terminal UI (TUI)
ratatui = { version = "0.24", features = ["all-widgets"] }
crossterm = "0.27"
reqwest = { version = "0.11", features = ["json"] }

geo = "0.28"
geo-types = "0.7"

# Machine Learning (for Phase 3)
linfa = "0.7"
linfa-linear = "0.7"

# bloXroute SDK
bloxroute-sdk = "1.0"

# Metrics and Observability
prometheus = "0.14"
axum = "0.7"
tower = "0.4"
tower-http = { version = "0.5", features = ["cors"] }
tracing-opentelemetry = "0.20"
metrics-exporter-prometheus = "0.12"

# Concurrency
dashmap = "5.5"
parking_lot = "0.12"
once_cell = "1.17"
lazy_static = "1.4"

# Statistics
statrs = "0.16"

# Wavelets (using a different crate)
# wavelets = "0.1.0"  # Commented out - not available

# Statistical analysis for regime detection and filtering
nalgebra = "0.32"  # For Kalman filter implementation
figment = { version = "0.10.19", features = ["toml", "env"] }



[dev-dependencies]
criterion = { version = "0.5", features = ["html_reports"] }
# Testing framework for mocking external dependencies
mockall = "0.12"
# Anvil for local blockchain testing
anvil = "0.1"
proptest = "1.2"
# Fuzzing support - cargo-fuzz is a binary, not a library dependency
# cargo-fuzz = "0.11"
# Temporary directories for testing
tempfile = "3.8"
# Additional dependencies for integration testing
regex = "1.10"
num_cpus = "1.16"

[workspace]
members = [
    ".",
]

[[bin]]
name = "listener"
path = "bin/listener.rs"

[[bin]]
name = "data_ingestor"
path = "bin/data_ingestor.rs"

[[bin]]
name = "tui_harness"
path = "bin/tui_harness.rs"

[[bin]]
name = "feature_exporter"
path = "bin/feature_exporter.rs"

[[bin]]
name = "graph_analyzer"
path = "src/bin/graph_analyzer.rs"

[[bin]]
name = "network_observer"
path = "src/bin/network_observer.rs"

[[bin]]
name = "seismic_analyzer"
path = "src/bin/seismic_analyzer.rs"

[[bin]]
name = "backtester"
path = "src/bin/backtester.rs"

[[bin]]
name = "demo_data_generator"
path = "src/bin/demo_data_generator.rs"

[[bin]]
name = "optimizer"
path = "src/bin/optimizer.rs"

[[bin]]
name = "mempool_backtester"
path = "src/bin/mempool_backtester.rs"

[[test]]
name = "geometric_strategy_benchmark"
path = "tests/geometric_strategy_benchmark.rs"
harness = false

[[test]]
name = "backend_integration_test"
path = "tests/integration/stargate_compass/integration_test.rs"

[[test]]
name = "backend_integration_simple"
path = "tests/test_backend_integration_simple.rs"
