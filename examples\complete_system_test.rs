// Complete system test for the elegant configuration system
// Run with: cargo run --example complete_system_test

use basilisk_bot::config::{Config, Settings, migration::*};
use std::env;
use std::sync::Arc;

fn main() -> Result<(), Box<dyn std::error::Error>> {
    println!("🧪 Complete Elegant Configuration System Test");
    println!("==============================================\n");

    // Test 1: Basic Configuration Loading
    test_basic_loading()?;
    
    // Test 2: Environment Variable Overrides
    test_environment_overrides()?;
    
    // Test 3: Validation System
    test_validation_system()?;
    
    // Test 4: Migration Compatibility
    test_migration_compatibility()?;
    
    // Test 5: Production Environment Rules
    test_production_rules()?;
    
    // Test 6: Multi-Chain Configuration
    test_multi_chain_config()?;
    
    // Test 7: Secrets Management
    test_secrets_management()?;
    
    println!("🎉 All tests passed! Elegant configuration system is ready for production.");
    println!("\n📋 System Capabilities Verified:");
    println!("  ✅ Layered configuration loading (base → env → env vars)");
    println!("  ✅ Comprehensive validation with business rules");
    println!("  ✅ Environment-specific safety constraints");
    println!("  ✅ Multi-chain configuration support");
    println!("  ✅ Secrets management via environment variables");
    println!("  ✅ Migration compatibility with existing code");
    println!("  ✅ Type-safe configuration with compile-time checks");

    Ok(())
}

fn test_basic_loading() -> Result<(), Box<dyn std::error::Error>> {
    println!("🔧 Test 1: Basic Configuration Loading");
    
    env::set_var("CONFIG_PATH", "config/elegant-production.toml");
    env::set_var("APP_ENV", "test");
    
    let config = Config::load()?;
    
    assert_eq!(config.app_name, "basilisk_bot");
    assert_eq!(config.strategy.kelly_fraction_cap, 0.25);
    assert!(!config.chains.is_empty());
    
    println!("  ✅ Configuration loaded successfully");
    println!("  📊 Loaded {} chains", config.chains.len());
    
    Ok(())
}

fn test_environment_overrides() -> Result<(), Box<dyn std::error::Error>> {
    println!("\n🌍 Test 2: Environment Variable Overrides");
    
    // Set overrides
    env::set_var("APP_STRATEGY__KELLY_FRACTION_CAP", "0.20");
    env::set_var("APP_EXECUTION__MAX_SLIPPAGE_BPS", "250");
    env::set_var("APP_LOG_LEVEL", "trace");
    
    let config = Config::load()?;
    
    assert_eq!(config.strategy.kelly_fraction_cap, 0.20);
    assert_eq!(config.execution.max_slippage_bps, 250);
    assert_eq!(config.log_level, "trace");
    
    println!("  ✅ Environment overrides working correctly");
    println!("  📊 Kelly fraction overridden to: {}", config.strategy.kelly_fraction_cap);
    
    // Clean up
    env::remove_var("APP_STRATEGY__KELLY_FRACTION_CAP");
    env::remove_var("APP_EXECUTION__MAX_SLIPPAGE_BPS");
    env::remove_var("APP_LOG_LEVEL");
    
    Ok(())
}

fn test_validation_system() -> Result<(), Box<dyn std::error::Error>> {
    println!("\n🔍 Test 3: Validation System");
    
    // Test valid configuration
    let config = Config::load()?;
    config.validate()?;
    println!("  ✅ Valid configuration passes validation");
    
    // Test invalid Kelly fraction
    env::set_var("APP_STRATEGY__KELLY_FRACTION_CAP", "1.5");
    let invalid_config = Config::load()?;
    let validation_result = invalid_config.validate();
    assert!(validation_result.is_err());
    println!("  ✅ Invalid Kelly fraction correctly rejected");
    
    // Test invalid slippage
    env::remove_var("APP_STRATEGY__KELLY_FRACTION_CAP");
    env::set_var("APP_EXECUTION__MAX_SLIPPAGE_BPS", "2000");
    let invalid_config = Config::load()?;
    let validation_result = invalid_config.validate();
    assert!(validation_result.is_err());
    println!("  ✅ Invalid slippage correctly rejected");
    
    // Clean up
    env::remove_var("APP_EXECUTION__MAX_SLIPPAGE_BPS");
    
    Ok(())
}

fn test_migration_compatibility() -> Result<(), Box<dyn std::error::Error>> {
    println!("\n🔄 Test 4: Migration Compatibility");
    
    // Load with new system
    let new_config = Arc::new(Config::load()?);
    
    // Convert to old format
    let old_settings = Arc::new(new_config.to_settings());
    
    // Verify compatibility
    assert_eq!(old_settings.app.name, new_config.app_name);
    
    let kelly_new = new_config.strategy.kelly_fraction_cap;
    let kelly_old = helpers::get_kelly_fraction(&old_settings);
    assert!((kelly_new - kelly_old).abs() < 0.001);
    
    println!("  ✅ Migration adapter preserves all critical data");
    println!("  📊 Kelly fraction: new={}, old={}", kelly_new, kelly_old);
    
    Ok(())
}

fn test_production_rules() -> Result<(), Box<dyn std::error::Error>> {
    println!("\n🏭 Test 5: Production Environment Rules");
    
    env::set_var("APP_ENV", "production");
    
    // Test production-safe configuration
    env::set_var("APP_STRATEGY__KELLY_FRACTION_CAP", "0.25");
    env::set_var("APP_EXECUTION__MAX_SLIPPAGE_BPS", "300");
    
    let config = Config::load()?;
    config.validate()?;
    println!("  ✅ Production-safe configuration passes validation");
    
    // Test production-unsafe configuration
    env::set_var("APP_STRATEGY__KELLY_FRACTION_CAP", "0.6");
    let unsafe_config = Config::load()?;
    let validation_result = unsafe_config.validate();
    assert!(validation_result.is_err());
    println!("  ✅ Production-unsafe configuration correctly rejected");
    
    // Clean up
    env::remove_var("APP_ENV");
    env::remove_var("APP_STRATEGY__KELLY_FRACTION_CAP");
    env::remove_var("APP_EXECUTION__MAX_SLIPPAGE_BPS");
    
    Ok(())
}

fn test_multi_chain_config() -> Result<(), Box<dyn std::error::Error>> {
    println!("\n🔗 Test 6: Multi-Chain Configuration");
    
    let config = Config::load()?;
    
    // Verify multiple chains are configured
    assert!(config.chains.len() >= 2);
    
    // Check specific chains
    assert!(config.chains.contains_key(&8453)); // Base
    assert!(config.chains.contains_key(&1));    // Ethereum
    
    // Verify chain-specific configuration
    let base_chain = &config.chains[&8453];
    assert_eq!(base_chain.name, "Base");
    assert!(!base_chain.rpc_url.is_empty());
    assert!(!base_chain.private_key_env_var.is_empty());
    
    println!("  ✅ Multi-chain configuration loaded correctly");
    println!("  📊 Configured chains: {:?}", config.chains.keys().collect::<Vec<_>>());
    
    Ok(())
}

fn test_secrets_management() -> Result<(), Box<dyn std::error::Error>> {
    println!("\n🔐 Test 7: Secrets Management");
    
    // Set test secrets
    env::set_var("APP_SECRETS__API_KEYS__BINANCE", "test_api_key");
    env::set_var("APP_SECRETS__PRIVATE_KEYS__BASE", "******************************************");
    
    let config = Config::load()?;
    
    // Verify secrets are loaded
    assert_eq!(config.secrets.api_keys.get("binance"), Some(&"test_api_key".to_string()));
    assert_eq!(config.secrets.private_keys.get("base"), Some(&"******************************************".to_string()));
    
    println!("  ✅ Secrets loaded from environment variables");
    println!("  📊 API keys: {}, Private keys: {}", 
             config.secrets.api_keys.len(), 
             config.secrets.private_keys.len());
    
    // Clean up
    env::remove_var("APP_SECRETS__API_KEYS__BINANCE");
    env::remove_var("APP_SECRETS__PRIVATE_KEYS__BASE");
    
    Ok(())
}
