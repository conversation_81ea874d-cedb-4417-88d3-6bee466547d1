// src/config.rs

use figment::{Figment, providers::{Format, Toml, Env}};
use rust_decimal::Decimal;
use rust_decimal_macros::dec;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use thiserror::Error;
use anyhow::{Result};

// Migration utilities for gradual transition to new config system
pub mod migration;

/// AUDIT-FIX: Configuration validation error types for Task 5
#[derive(Error, Debug)]
pub enum ConfigValidationError {
    #[error("Configuration validation failed: {0:?}")]
    ValidationFailed(Vec<String>),
    #[error("Invalid parameter range: {parameter} = {value}, expected {expected_range}")]
    InvalidRange {
        parameter: String,
        value: String,
        expected_range: String,
    },
    #[error("Missing required parameter: {0}")]
    MissingParameter(String),
}

/// New canonical configuration structure following the elegant configuration guide
#[derive(Debug, Deserialize, Clone)]
pub struct Config {
    pub app_name: String,
    pub log_level: String,
    #[serde(default)]
    pub chains: HashMap<u64, ChainConfig>,
    pub strategy: StrategyConfig,
    pub execution: ExecutionConfig,
    pub secrets: Secrets,
    pub scoring: ScoringConfig,
}

/// Chain-specific configuration
#[derive(Debug, Deserialize, Clone)]
pub struct ChainConfig {
    pub name: String,
    pub rpc_url: String,
    pub max_gas_price: u64,
    pub private_key_env_var: String, // Reference to the env var
    pub contracts: ContractAddresses,
    pub dex: DexConfig,
}

/// Contract addresses for each chain
#[derive(Debug, Deserialize, Clone)]
pub struct ContractAddresses {
    pub stargate_compass_v1: Option<String>,
    pub multicall: Option<String>,
    // Add other contract addresses here
}

/// DEX configuration for each chain
#[derive(Debug, Deserialize, Clone)]
pub struct DexConfig {
    pub degen_swap_router: Option<String>,
    pub uniswap_v2_router: Option<String>,
    // Add other DEX-related configs
}

/// Strategy configuration
#[derive(Debug, Deserialize, Clone)]
pub struct StrategyConfig {
    pub kelly_fraction_cap: f64,
    pub min_profitability_bps: u64,
    pub enabled_strategies: Vec<String>,
    // ... other strategy parameters
}

/// Execution configuration
#[derive(Debug, Deserialize, Clone)]
pub struct ExecutionConfig {
    pub max_slippage_bps: u64,
    pub gas_limit_multiplier: f64,
    pub max_gas_price_gwei: u64,
    // ... other execution parameters
}

/// Secrets loaded from environment variables
#[derive(Debug, Deserialize, Clone)]
pub struct Secrets {
    // This struct will be populated from environment variables
    #[serde(default)]
    pub api_keys: HashMap<String, String>,
    #[serde(default)]
    pub private_keys: HashMap<String, String>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct ScoringConfig {
    pub quality_ratio_floor: Decimal,
    pub risk_aversion_k: Decimal,
    pub regime_multiplier_retail_fomo: Decimal,
    pub regime_multiplier_high_vol: Decimal,
    pub regime_multiplier_calm: Decimal,
    pub regime_multiplier_gas_war_penalty: Decimal,
    pub temporal_harmonics_weight: Decimal,
    pub geometric_score_weight: Decimal,
    pub network_resonance_weight: Decimal,
}

impl Default for ScoringConfig {
    fn default() -> Self {
        Self {
            quality_ratio_floor: dec!(0.3),
            risk_aversion_k: dec!(0.5),
            regime_multiplier_retail_fomo: dec!(1.2),
            regime_multiplier_high_vol: dec!(0.8),
            regime_multiplier_calm: dec!(1.0),
            regime_multiplier_gas_war_penalty: dec!(0.5),
            temporal_harmonics_weight: dec!(0.33),
            geometric_score_weight: dec!(0.33),
            network_resonance_weight: dec!(0.34),
        }
    }
}

impl Config {
    /// Load configuration using figment with layered approach
    pub fn load() -> Result<Self, figment::Error> {
        let config_path = std::env::var("CONFIG_PATH").unwrap_or_else(|_| "config/default.toml".into());
        let env_profile = std::env::var("APP_ENV").unwrap_or_else(|_| "local".into());
        let profile_path = format!("config/{}.toml", env_profile);

        Figment::new()
            // 1. Load base configuration
            .merge(Toml::file(config_path))
            // 2. Load environment-specific configuration (e.g., production.toml), which can override the base.
            .merge(Toml::file(profile_path).nested())
            // 3. Load secrets and overrides from environment variables.
            // e.g., APP_SECRETS_API_KEYS_BINANCE=...
            // e.g., APP_STRATEGY_KELLY_FRACTION_CAP=0.25
            .merge(Env::prefixed("APP_").split("__"))
            .extract()
    }

    /// Comprehensive validation for semantic correctness
    /// This goes beyond type safety to catch logical and business rule violations
    pub fn validate(&self) -> Result<()> {
        // Strategy validation
        self.validate_strategy_config()?;

        // Execution validation
        self.validate_execution_config()?;

        // Chain validation
        self.validate_chains_config()?;

        // Cross-field validation
        self.validate_cross_field_consistency()?;

        // Business logic validation
        self.validate_business_rules()?;

        Ok(())
    }

    /// Validate strategy configuration parameters
    fn validate_strategy_config(&self) -> Result<()> {
        // Kelly fraction cap validation (critical for risk management)
        if self.strategy.kelly_fraction_cap <= 0.0 || self.strategy.kelly_fraction_cap > 1.0 {
            anyhow::bail!(
                "strategy.kelly_fraction_cap must be between 0.0 and 1.0, got: {}. \
                Kelly criterion requires fraction ",
                self.strategy.kelly_fraction_cap
            );
        }

        // Warn if Kelly fraction is too aggressive
        if self.strategy.kelly_fraction_cap > 0.5 {
            tracing::warn!(
                "Kelly fraction cap {} is very aggressive. Consider values ",
                self.strategy.kelly_fraction_cap
            );
        }

        // Minimum profitability validation
        if self.strategy.min_profitability_bps == 0 {
            anyhow::bail!("strategy.min_profitability_bps must be positive to ensure profitable trades");
        }

        if self.strategy.min_profitability_bps > 10000 {
            anyhow::bail!(
                "strategy.min_profitability_bps cannot exceed 10000 (100%), got: {}. \
                ",
                self.strategy.min_profitability_bps
            );
        }

        // Strategy enablement validation
        if self.strategy.enabled_strategies.is_empty() {
            anyhow::bail!("At least one strategy must be enabled");
        }

        // Validate strategy names
        let valid_strategies = ["zen_geometer", "basilisk_gaze", "pilot_fish", "aetheric_resonance"];
        for strategy in &self.strategy.enabled_strategies {
            if !valid_strategies.contains(&strategy.as_str()) {
                anyhow::bail!(
                    "Unknown strategy '{}'. Valid strategies: {:?}",
                    strategy, valid_strategies
                );
            }
        }

        Ok(())
    }

    /// Validate execution configuration parameters
    fn validate_execution_config(&self) -> Result<()> {
        // Slippage validation (0.01% to 10% is reasonable)
        if self.execution.max_slippage_bps == 0 {
            anyhow::bail!("execution.max_slippage_bps must be positive");
        }

        if self.execution.max_slippage_bps > 1000 {
            anyhow::bail!(
                "execution.max_slippage_bps cannot exceed 1000 (10%), got: {}. \
                High slippage increases MEV vulnerability.",
                self.execution.max_slippage_bps
            );
        }

        // Warn for high slippage
        if self.execution.max_slippage_bps > 500 {
            tracing::warn!(
                "High slippage tolerance: {}bps ({}%). Consider lower values to reduce MEV risk.",
                self.execution.max_slippage_bps,
                self.execution.max_slippage_bps as f64 / 100.0
            );
        }

        // Gas limit multiplier validation (1.0x to 3.0x is reasonable)
        if self.execution.gas_limit_multiplier < 1.0 {
            anyhow::bail!(
                "execution.gas_limit_multiplier must be ",
                self.execution.gas_limit_multiplier
            );
        }

        if self.execution.gas_limit_multiplier > 3.0 {
            anyhow::bail!(
                "execution.gas_limit_multiplier cannot exceed 3.0, got: {}. \
                Excessive gas limits waste funds.",
                self.execution.gas_limit_multiplier
            );
        }

        // Gas price validation (reasonable bounds for mainnet)
        if self.execution.max_gas_price_gwei == 0 {
            anyhow::bail!("execution.max_gas_price_gwei must be positive");
        }

        if self.execution.max_gas_price_gwei > 1000 {
            anyhow::bail!(
                "execution.max_gas_price_gwei cannot exceed 1000 gwei, got: {}. \
                Extremely high gas prices are likely configuration errors.",
                self.execution.max_gas_price_gwei
            );
        }

        // Warn for very high gas prices
        if self.execution.max_gas_price_gwei > 200 {
            tracing::warn!(
                "High max gas price: {} gwei. Ensure this is intentional for your network.",
                self.execution.max_gas_price_gwei
            );
        }

        Ok(())
    }

    /// Validate chain configurations
    fn validate_chains_config(&self) -> Result<()> {
        if self.chains.is_empty() {
            anyhow::bail!("At least one chain must be configured");
        }

        for (chain_id, chain_config) in &self.chains {
            // Chain ID validation
            if *chain_id == 0 {
                anyhow::bail!("Chain ID cannot be zero");
            }

            // Validate known chain IDs
            let known_chains = [
                (1, "Ethereum Mainnet"),
                (8453, "Base"),
                (137, "Polygon"),
                (42161, "Arbitrum One"),
                (10, "Optimism"),
                (56, "BSC"),
                (43114, "Avalanche"),
            ];

            let is_known_chain = known_chains.iter().any(|(id, _)| id == chain_id);
            if !is_known_chain {
                tracing::warn!(
                    "Unknown chain ID: {}. Ensure this is correct for chain '{}'",
                    chain_id, chain_config.name
                );
            }

            // RPC URL validation
            if chain_config.rpc_url.is_empty() {
                anyhow::bail!("Chain {} has empty rpc_url", chain_id);
            }

            // Basic URL format validation
            if !chain_config.rpc_url.starts_with("http://") &&
               !chain_config.rpc_url.starts_with("https://") &&
               !chain_config.rpc_url.starts_with("wss://") &&
               !chain_config.rpc_url.starts_with("ws://") {
                anyhow::bail!(
                    "Chain {} RPC URL must start with http://, https://, ws://, or wss://, got: {}",
                    chain_id, chain_config.rpc_url
                );
            }

            // Gas price validation
            if chain_config.max_gas_price == 0 {
                anyhow::bail!("Chain {} max_gas_price must be positive", chain_id);
            }

            // Private key environment variable validation
            if chain_config.private_key_env_var.is_empty() {
                anyhow::bail!("Chain {} private_key_env_var cannot be empty", chain_id);
            }

            // Validate environment variable naming convention
            if !chain_config.private_key_env_var.ends_with("_PRIVATE_KEY") {
                tracing::warn!(
                    "Chain {} private key env var '{}' doesn't follow *_PRIVATE_KEY convention",
                    chain_id, chain_config.private_key_env_var
                );
            }

            // Contract address validation (if provided)
            if let Some(stargate) = &chain_config.contracts.stargate_compass_v1 {
                if !Self::is_valid_ethereum_address(stargate) {
                    anyhow::bail!(
                        "Chain {} stargate_compass_v1 address is invalid: {}",
                        chain_id, stargate
                    );
                }
            }

            if let Some(multicall) = &chain_config.contracts.multicall {
                if !Self::is_valid_ethereum_address(multicall) {
                    anyhow::bail!(
                        "Chain {} multicall address is invalid: {}",
                        chain_id, multicall
                    );
                }
            }
        }

        Ok(())
    }

    /// Validate cross-field consistency
    fn validate_cross_field_consistency(&self) -> Result<()> {
        // Ensure strategy profitability is achievable given execution costs
        let min_profit_usd = (self.strategy.min_profitability_bps as f64) / 10000.0 * 100.0; // Rough estimate
        let max_slippage_cost = (self.execution.max_slippage_bps as f64) / 10000.0 * 100.0;

        if min_profit_usd <= max_slippage_cost * 2.0 {
            tracing::warn!(
                "Minimum profitability ({}bps) may be too low relative to max slippage ({}bps). \
                Consider higher profitability targets to account for execution costs.",
                self.strategy.min_profitability_bps, self.execution.max_slippage_bps
            );
        }

        // Validate Kelly fraction vs enabled strategies
        if self.strategy.enabled_strategies.len() > 3 && self.strategy.kelly_fraction_cap > 0.3 {
            tracing::warn!(
                "Running {} strategies with Kelly fraction {} may be too aggressive. \
                Consider lower Kelly fraction for multi-strategy setups.",
                self.strategy.enabled_strategies.len(), self.strategy.kelly_fraction_cap
            );
        }

        Ok(())
    }

    /// Validate business rules and trading logic
    fn validate_business_rules(&self) -> Result<()> {
        // Environment-specific validations
        let env = std::env::var("APP_ENV").unwrap_or_else(|_| "local".into());

        match env.as_str() {
            "production" => {
                // Production-specific validations
                if self.strategy.kelly_fraction_cap > 0.5 {
                    anyhow::bail!(
                        "Kelly fraction cap {} is too aggressive for production. Maximum 0.5 allowed.",
                        self.strategy.kelly_fraction_cap
                    );
                }

                if self.execution.max_slippage_bps > 300 {
                    anyhow::bail!(
                        "Slippage tolerance {}bps is too high for production. Maximum 300bps allowed.",
                        self.execution.max_slippage_bps
                    );
                }

                // Ensure all chains have reasonable gas limits for production
                for (chain_id, _) in &self.chains {
                    if *chain_id == 1 && self.execution.max_gas_price_gwei > 100 {
                        tracing::warn!(
                            "High gas price limit for Ethereum mainnet in production: {} gwei",
                            self.execution.max_gas_price_gwei
                        );
                    }
                }
            }
            "staging" => {
                // Staging-specific validations
                if self.strategy.kelly_fraction_cap > 0.75 {
                    tracing::warn!(
                        "Kelly fraction cap {} is high for staging environment",
                        self.strategy.kelly_fraction_cap
                    );
                }
            }
            _ => {
                // Development/local validations are more relaxed
                if self.strategy.kelly_fraction_cap > 0.9 {
                    tracing::warn!(
                        "Kelly fraction cap {} is extremely high even for development",
                        self.strategy.kelly_fraction_cap
                    );
                }
            }
        }

        Ok(())
    }

    /// Validate Ethereum address format
    fn is_valid_ethereum_address(address: &str) -> bool {
        // Basic validation: starts with 0x and is 42 characters long
        address.starts_with("0x") &&
        address.len() == 42 &&
        address[2..].chars().all(|c| c.is_ascii_hexdigit())
    }
}

#[cfg(test)]
mod elegant_config_tests {
    use super::*;
    use std::env;

    #[test]
    fn test_new_config_loading() {
        // Set environment variables for testing
        env::set_var("CONFIG_PATH", "config/elegant-minimal.toml");
        env::set_var("APP_ENV", "test");

        // Test loading the new configuration
        let result = Config::load();

        match result {
            Ok(config) => {
                assert_eq!(config.app_name, "basilisk_bot");
                assert_eq!(config.log_level, "info");
                assert_eq!(config.strategy.kelly_fraction_cap, 0.25);
                assert!(config.chains.contains_key(&8453));

                // Test validation
                assert!(config.validate().is_ok());

                println!("✅ New configuration system loaded successfully!");
                println!("Chains configured: {:?}", config.chains.keys().collect::<Vec<_>>());
            }
            Err(e) => {
                println!("❌ Failed to load configuration: {}", e);
                // Don't panic in test, just print the error for debugging
            }
        }
    }

    #[test]
    fn test_enhanced_validation() {
        use std::env;

        // Test valid configuration
        env::set_var("CONFIG_PATH", "config/elegant-minimal.toml");
        env::set_var("APP_ENV", "test");

        match Config::load() {
            Ok(config) => {
                println!("✅ Configuration loaded for validation test");

                // Test comprehensive validation
                match config.validate() {
                    Ok(()) => {
                        println!("✅ Enhanced validation passed!");
                    }
                    Err(e) => {
                        println!("❌ Validation failed: {}", e);
                        // Don't panic in test, just report
                    }
                }
            }
            Err(e) => {
                println!("❌ Failed to load config for validation test: {}", e);
            }
        }
    }

    #[test]
    fn test_validation_edge_cases() {
        // Test invalid Kelly fraction
        let mut config = Config {
            app_name: "test".to_string(),
            log_level: "info".to_string(),
            chains: std::collections::HashMap::new(),
            strategy: StrategyConfig {
                kelly_fraction_cap: 1.5, // Invalid: > 1.0
                min_profitability_bps: 50,
                enabled_strategies: vec!["zen_geometer".to_string()],
            },
            execution: ExecutionConfig {
                max_slippage_bps: 500,
                gas_limit_multiplier: 1.2,
                max_gas_price_gwei: 100,
            },
            secrets: Secrets {
                api_keys: std::collections::HashMap::new(),
                private_keys: std::collections::HashMap::new(),
            },
            scoring: ScoringConfig::default(),
        };

        // Should fail validation
        assert!(config.validate().is_err());
        println!("✅ Invalid Kelly fraction correctly rejected");

        // Fix Kelly fraction, test invalid slippage
        config.strategy.kelly_fraction_cap = 0.25;
        config.execution.max_slippage_bps = 2000; // Invalid: > 1000

        assert!(config.validate().is_err());
        println!("✅ Invalid slippage correctly rejected");

        // Fix slippage, test empty chains
        config.execution.max_slippage_bps = 500;
        // chains is already empty

        assert!(config.validate().is_err());
        println!("✅ Empty chains correctly rejected");
    }
}