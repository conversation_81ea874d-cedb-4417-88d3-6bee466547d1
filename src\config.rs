// src/config.rs

use config::{Config as OldConfig, ConfigError, Environment, File};
use figment::{Figment, providers::{Format, Toml, Env}};
use ethers::types::Address;
use rust_decimal::Decimal;
use rust_decimal_macros::dec;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::env;
use std::path::Path;
use thiserror::Error;
use anyhow::{Context, Result};

// Migration utilities for gradual transition to new config system
pub mod migration;

/// AUDIT-FIX: Configuration validation error types for Task 5
#[derive(Error, Debug)]
pub enum ConfigValidationError {
    #[error("Configuration validation failed: {0:?}")]
    ValidationFailed(Vec<String>),
    #[error("Invalid parameter range: {parameter} = {value}, expected {expected_range}")]
    InvalidRange {
        parameter: String,
        value: String,
        expected_range: String,
    },
    #[error("Missing required parameter: {0}")]
    MissingParameter(String),
}

/// Main configuration structure
#[derive(Debug, Serialize, Deserialize, <PERSON><PERSON>, Default)]
pub struct Settings {
    pub app: AppConfig,
    pub execution: ExecutionConfig,
    pub risk: RiskConfig,
    pub strategies: StrategiesConfig,
    pub scoring: ScoringConfig,
    pub chains: HashMap<String, ChainConfig>,
    // AUDIT-FIX: Add missing fields expected by other modules
    pub database: DatabaseConfig,
    pub redis: RedisConfig,
    pub nats: NatsConfig,
    pub rpc: RpcConfig,
    pub cex: Vec<CexConfig>,
    pub dry_run: bool,
    pub active_chain_id: u64,
    pub bridges: BridgeConfig,
    pub aetheric_resonance_engine: AethericResonanceEngineConfig,
    pub alerting: AlertingConfig,
    pub chainlink_feeds: HashMap<String, String>,
    pub scanners: ScannerSettings,
    pub authorized_operators: Vec<String>,
    pub manifold: ManifoldConfig,
}

#[derive(Debug, Serialize, Deserialize, Clone, Default)]
pub struct AppConfig {
    pub name: String,
    pub version: String,
    pub environment: String,
}

#[derive(Debug, Serialize, Deserialize, Clone, Default)]
pub struct ExecutionConfig {
    pub max_slippage_bps: u64,
    pub gas_limit_multiplier: Decimal,
    pub max_gas_price_gwei: u64,
}

#[derive(Debug, Serialize, Deserialize, Clone, Default)]
pub struct RiskConfig {
    pub max_position_size_usd: Decimal,
    pub max_daily_loss_usd: Decimal,
    pub kelly_fraction_cap: Decimal,
}

#[derive(Debug, Serialize, Deserialize, Clone, Default)]
pub struct StrategiesConfig {
    pub enabled_strategies: Vec<String>,
    pub aetheric_resonance_engine: AethericResonanceEngineConfig,
}

#[derive(Debug, Serialize, Deserialize, Clone, Default)]
pub struct AethericResonanceEngineConfig {
    pub min_resonance_score: Decimal,
    // AUDIT-FIX: Make chain IDs configurable instead of hardcoded
    pub base_chain_id: Option<u64>,
    pub degen_chain_id: Option<u64>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct ScoringConfig {
    pub quality_ratio_floor: Decimal,
    pub risk_aversion_k: Decimal,
    pub regime_multiplier_retail_fomo: Decimal,
    pub regime_multiplier_high_vol: Decimal,
    pub regime_multiplier_calm: Decimal,
    pub regime_multiplier_gas_war_penalty: Decimal,
    pub temporal_harmonics_weight: Decimal,
    pub geometric_score_weight: Decimal,
    pub network_resonance_weight: Decimal,
}

#[derive(Debug, Serialize, Deserialize, Clone, Default)]
pub struct ChainConfig {
    pub name: String,
    pub chain_id: u64,
    pub rpc_endpoints: Vec<RpcEndpoint>,
    pub tokens: HashMap<String, TokenConfig>,
    pub dexes: HashMap<String, DexConfig>,
    pub contracts: HashMap<String, ContractConfig>,
}

#[derive(Debug, Serialize, Deserialize, Clone, Default)]
pub struct RpcEndpoint {
    pub url: String,
    pub priority: u32,
    pub timeout_ms: u64,
}

#[derive(Debug, Serialize, Deserialize, Clone, Default)]
pub struct TokenConfig {
    pub address: String,
    pub decimals: u8,
    pub symbol: String,
}

#[derive(Debug, Serialize, Deserialize, Clone, Default)]
pub struct DexConfig {
    pub name: String,
    pub router_address: String,
    pub factory_address: String,
}

#[derive(Debug, Serialize, Deserialize, Clone, Default)]
pub struct ContractConfig {
    pub address: String,
    pub abi_path: Option<String>,
}

// AUDIT-FIX: Add missing configuration structures for compilation
#[derive(Debug, Serialize, Deserialize, Clone, Default)]
pub struct DatabaseConfig {
    pub url: String,
    pub max_connections: u32,
}

#[derive(Debug, Serialize, Deserialize, Clone, Default)]
pub struct RedisConfig {
    pub url: String,
    pub max_connections: u32,
}

#[derive(Debug, Serialize, Deserialize, Clone, Default)]
pub struct NatsConfig {
    pub url: String,
    pub max_reconnects: u32,
}

#[derive(Debug, Serialize, Deserialize, Clone, Default)]
pub struct RpcConfig {
    pub url: String,
    pub timeout_ms: u64,
}

#[derive(Debug, Serialize, Deserialize, Clone, Default)]
pub struct CexConfig {
    pub name: String,
    pub api_key: String,
    pub api_secret: String,
}

#[derive(Debug, Serialize, Deserialize, Clone, Default)]
pub struct AlertingConfig {
    pub enabled: bool,
    pub webhook_url: Option<String>,
}

#[derive(Debug, Serialize, Deserialize, Clone, Default)]
pub struct BridgeConfig {
    pub routes: Vec<BridgeRoute>,
}

#[derive(Debug, Serialize, Deserialize, Clone, Default)]
pub struct BridgeRoute {
    pub from_chain: u64,
    pub to_chain: u64,
    pub bridge_address: String,
}

#[derive(Debug, Serialize, Deserialize, Clone, Default)]
pub struct ScannerSettings {
    pub enabled: bool,
    pub interval_ms: u64,
}

#[derive(Debug, Serialize, Deserialize, Clone, Default)]
pub struct ManifoldConfig {
    pub assets: Vec<String>,
}

#[derive(Debug, Serialize, Deserialize, Clone, Default)]
pub struct PilotFishScannerSettings {
    pub enabled: bool,
    pub interval_ms: u64,
    pub min_profit_usd: Decimal,
}

impl Default for ScoringConfig {
    fn default() -> Self {
        Self {
            quality_ratio_floor: dec!(0.3),
            risk_aversion_k: dec!(0.5),
            regime_multiplier_retail_fomo: dec!(1.2),
            regime_multiplier_high_vol: dec!(0.8),
            regime_multiplier_calm: dec!(1.0),
            regime_multiplier_gas_war_penalty: dec!(0.5),
            temporal_harmonics_weight: dec!(0.33),
            geometric_score_weight: dec!(0.33),
            network_resonance_weight: dec!(0.34),
        }
    }
}

impl Settings {
    /// AUDIT-FIX: Implement configuration loading with proper error handling
    pub fn new(config_path: Option<&str>) -> Result<Self, ConfigError> {
        let mut builder = Config::builder();
        
        // Load default configuration
        builder = builder.add_source(File::with_name("config/default").required(false));
        
        // Load environment-specific configuration if provided
        if let Some(path) = config_path {
            builder = builder.add_source(File::with_name(path).required(false));
        }
        
        // Load environment variables
        builder = builder.add_source(Environment::with_prefix("BASILISK"));
        
        let config = builder.build()?;
        let settings: Settings = config.try_deserialize()?;
        
        // AUDIT-FIX: Validate configuration after loading
        if let Err(e) = settings.validate() {
            return Err(ConfigError::Message(format!("Configuration validation failed: {}", e)));
        }
        
        Ok(settings)
    }
    
    /// AUDIT-FIX: Comprehensive configuration validation
    pub fn validate(&self) -> Result<(), ConfigValidationError> {
        let mut errors = Vec::new();
        
        // Validate scoring configuration
        if let Err(e) = self.scoring.validate() {
            errors.push(format!("Scoring config: {}", e));
        }
        
        // Validate execution configuration
        if let Err(e) = self.execution.validate() {
            errors.push(format!("Execution config: {}", e));
        }
        
        // Validate risk configuration
        if let Err(e) = self.risk.validate() {
            errors.push(format!("Risk config: {}", e));
        }
        
        // Validate chain configurations
        for (chain_name, chain_config) in &self.chains {
            if let Err(e) = chain_config.validate() {
                errors.push(format!("Chain '{}': {}", chain_name, e));
            }
        }
        
        if errors.is_empty() {
            Ok(())
        } else {
            Err(ConfigValidationError::ValidationFailed(errors))
        }
    }
}

impl ScoringConfig {
    /// AUDIT-FIX: Validate all parameter ranges and constraints for Task 5
    pub fn validate(&self) -> Result<(), ConfigValidationError> {
        let mut errors = Vec::new();
        
        // AUDIT-FIX: Validate pillar weights sum to 1.0 with tolerance
        let total_weight = self.temporal_harmonics_weight 
            + self.geometric_score_weight 
            + self.network_resonance_weight;
        let weight_tolerance = dec!(0.01); // 1% tolerance
        
        if (total_weight - dec!(1.0)).abs() > weight_tolerance {
            errors.push(format!(
                "Pillar weights must sum to 1.0 (+/- {}), got: {} (temporal: {}, geometric: {}, network: {})",
                weight_tolerance, total_weight,
                self.temporal_harmonics_weight,
                self.geometric_score_weight,
                self.network_resonance_weight
            ));
        }
        
        // Validate individual weight ranges [0.0, 1.0]
        if self.temporal_harmonics_weight < dec!(0.0) || self.temporal_harmonics_weight > dec!(1.0) {
            errors.push(format!("temporal_harmonics_weight must be in [0.0, 1.0], got: {}", self.temporal_harmonics_weight));
        }
        if self.geometric_score_weight < dec!(0.0) || self.geometric_score_weight > dec!(1.0) {
            errors.push(format!("geometric_score_weight must be in [0.0, 1.0], got: {}", self.geometric_score_weight));
        }
        if self.network_resonance_weight < dec!(0.0) || self.network_resonance_weight > dec!(1.0) {
            errors.push(format!("network_resonance_weight must be in [0.0, 1.0], got: {}", self.network_resonance_weight));
        }
        
        // AUDIT-FIX: Add risk aversion parameter bounds checking
        if self.risk_aversion_k < dec!(0.0) || self.risk_aversion_k > dec!(2.0) {
            errors.push(format!("risk_aversion_k must be in [0.0, 2.0] for Kelly Criterion, got: {}", self.risk_aversion_k));
        }
        
        // Validate quality ratio floor
        if self.quality_ratio_floor < dec!(0.0) || self.quality_ratio_floor > dec!(1.0) {
            errors.push(format!("quality_ratio_floor must be in [0.0, 1.0], got: {}", self.quality_ratio_floor));
        }
        
        // Validate regime multipliers (reasonable bounds)
        let multipliers = [
            ("regime_multiplier_retail_fomo", self.regime_multiplier_retail_fomo),
            ("regime_multiplier_high_vol", self.regime_multiplier_high_vol),
            ("regime_multiplier_calm", self.regime_multiplier_calm),
            ("regime_multiplier_gas_war_penalty", self.regime_multiplier_gas_war_penalty),
        ];
        
        for (name, value) in multipliers {
            if value < dec!(0.1) || value > dec!(5.0) {
                errors.push(format!("{} must be in [0.1, 5.0] for reasonable trading, got: {}", name, value));
            }
        }
        
        if errors.is_empty() {
            Ok(())
        } else {
            Err(ConfigValidationError::ValidationFailed(errors))
        }
    }
}

impl ExecutionConfig {
    /// AUDIT-FIX: Validate execution configuration parameters
    pub fn validate(&self) -> Result<(), ConfigValidationError> {
        let mut errors = Vec::new();
        
        // Validate slippage bounds (0.01% to 10%)
        if self.max_slippage_bps == 0 || self.max_slippage_bps > 1000 {
            errors.push(format!("max_slippage_bps must be in [1, 1000] (0.01% to 10%), got: {}", self.max_slippage_bps));
        }
        
        // Validate gas limit multiplier (1.0x to 3.0x)
        if self.gas_limit_multiplier < dec!(1.0) || self.gas_limit_multiplier > dec!(3.0) {
            errors.push(format!("gas_limit_multiplier must be in [1.0, 3.0], got: {}", self.gas_limit_multiplier));
        }
        
        // Validate max gas price (1 to 1000 gwei)
        if self.max_gas_price_gwei == 0 || self.max_gas_price_gwei > 1000 {
            errors.push(format!("max_gas_price_gwei must be in [1, 1000], got: {}", self.max_gas_price_gwei));
        }
        
        if errors.is_empty() {
            Ok(())
        } else {
            Err(ConfigValidationError::ValidationFailed(errors))
        }
    }
}

impl RiskConfig {
    /// AUDIT-FIX: Validate risk management parameters
    pub fn validate(&self) -> Result<(), ConfigValidationError> {
        let mut errors = Vec::new();
        
        // Validate position size (must be positive)
        if self.max_position_size_usd <= dec!(0.0) {
            errors.push(format!("max_position_size_usd must be positive, got: {}", self.max_position_size_usd));
        }
        
        // Validate daily loss limit (must be positive)
        if self.max_daily_loss_usd <= dec!(0.0) {
            errors.push(format!("max_daily_loss_usd must be positive, got: {}", self.max_daily_loss_usd));
        }
        
        // Validate Kelly fraction cap (0.01 to 0.25 for safety)
        if self.kelly_fraction_cap < dec!(0.01) || self.kelly_fraction_cap > dec!(0.25) {
            errors.push(format!("kelly_fraction_cap must be in [0.01, 0.25] for safety, got: {}", self.kelly_fraction_cap));
        }
        
        if errors.is_empty() {
            Ok(())
        } else {
            Err(ConfigValidationError::ValidationFailed(errors))
        }
    }
}

impl ChainConfig {
    /// AUDIT-FIX: Validate chain configuration
    pub fn validate(&self) -> Result<(), ConfigValidationError> {
        let mut errors = Vec::new();
        
        // Validate chain ID is reasonable
        if self.chain_id == 0 {
            errors.push("chain_id cannot be zero".to_string());
        }
        
        // Validate at least one RPC endpoint
        if self.rpc_endpoints.is_empty() {
            errors.push("At least one RPC endpoint must be configured".to_string());
        }
        
        // Validate RPC endpoints
        for (i, endpoint) in self.rpc_endpoints.iter().enumerate() {
            if let Err(e) = endpoint.validate() {
                errors.push(format!("RPC endpoint {}: {}", i, e));
            }
        }
        
        if errors.is_empty() {
            Ok(())
        } else {
            Err(ConfigValidationError::ValidationFailed(errors))
        }
    }
}

impl RpcEndpoint {
    /// Validate RPC endpoint configuration
    pub fn validate(&self) -> Result<(), ConfigValidationError> {
        let mut errors = Vec::new();
        
        // Validate URL format
        if self.url.is_empty() {
            errors.push("RPC URL cannot be empty".to_string());
        } else if !self.url.starts_with("http://") && !self.url.starts_with("https://") && !self.url.starts_with("ws://") && !self.url.starts_with("wss://") {
            errors.push(format!("RPC URL must start with http://, https://, ws://, or wss://, got: {}", self.url));
        }
        
        // Validate timeout (1 second to 60 seconds)
        if self.timeout_ms < 1000 || self.timeout_ms > 60000 {
            errors.push(format!("timeout_ms must be in [1000, 60000] (1-60 seconds), got: {}", self.timeout_ms));
        }
        
        if errors.is_empty() {
            Ok(())
        } else {
            Err(ConfigValidationError::ValidationFailed(errors))
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_scoring_config_validation_valid() {
        let config = ScoringConfig::default();
        assert!(config.validate().is_ok());
    }
    
    #[test]
    fn test_scoring_config_validation_invalid_weights() {
        let mut config = ScoringConfig::default();
        config.temporal_harmonics_weight = dec!(0.5);
        config.geometric_score_weight = dec!(0.5);
        config.network_resonance_weight = dec!(0.5); // Sum = 1.5, should fail
        
        assert!(config.validate().is_err());
    }
    
    #[test]
    fn test_execution_config_validation() {
        let mut config = ExecutionConfig::default();
        config.max_slippage_bps = 50; // 0.5%
        config.gas_limit_multiplier = dec!(1.5);
        config.max_gas_price_gwei = 100;
        
        assert!(config.validate().is_ok());
    }
    
    #[test]
    fn test_risk_config_validation() {
        let mut config = RiskConfig::default();
        config.max_position_size_usd = dec!(1000.0);
        config.max_daily_loss_usd = dec!(100.0);
        config.kelly_fraction_cap = dec!(0.1);
        
        assert!(config.validate().is_ok());
    }
}

// ============================================================================
// NEW ELEGANT CONFIGURATION SYSTEM (Following the Guide)
// ============================================================================

/// New canonical configuration structure following the elegant configuration guide
/// This will replace Settings once migration is complete
#[derive(Debug, Deserialize, Clone)]
pub struct Config {
    pub app_name: String,
    pub log_level: String,
    #[serde(default)]
    pub chains: HashMap<u64, ChainConfig>,
    pub strategy: StrategyConfig,
    pub execution: ExecutionConfig,
    pub secrets: Secrets,
}

/// Chain-specific configuration
#[derive(Debug, Deserialize, Clone)]
pub struct ChainConfig {
    pub name: String,
    pub rpc_url: String,
    pub max_gas_price: u64,
    pub private_key_env_var: String, // Reference to the env var
    pub contracts: ContractAddresses,
    pub dex: DexConfig,
}

/// Contract addresses for each chain
#[derive(Debug, Deserialize, Clone)]
pub struct ContractAddresses {
    pub stargate_compass_v1: Option<String>,
    pub multicall: Option<String>,
    // Add other contract addresses here
}

/// DEX configuration for each chain
#[derive(Debug, Deserialize, Clone)]
pub struct DexConfig {
    pub degen_swap_router: Option<String>,
    pub uniswap_v2_router: Option<String>,
    // Add other DEX-related configs
}

/// Strategy configuration
#[derive(Debug, Deserialize, Clone)]
pub struct StrategyConfig {
    pub kelly_fraction_cap: f64,
    pub min_profitability_bps: u64,
    pub enabled_strategies: Vec<String>,
    // ... other strategy parameters
}

/// Execution configuration
#[derive(Debug, Deserialize, Clone)]
pub struct ExecutionConfig {
    pub max_slippage_bps: u64,
    pub gas_limit_multiplier: f64,
    pub max_gas_price_gwei: u64,
    // ... other execution parameters
}

/// Secrets loaded from environment variables
#[derive(Debug, Deserialize, Clone)]
pub struct Secrets {
    // This struct will be populated from environment variables
    #[serde(default)]
    pub api_keys: HashMap<String, String>,
    #[serde(default)]
    pub private_keys: HashMap<String, String>,
}

impl Config {
    /// Load configuration using figment with layered approach
    pub fn load() -> Result<Self, figment::Error> {
        let config_path = std::env::var("CONFIG_PATH").unwrap_or_else(|_| "config/default.toml".into());
        let env_profile = std::env::var("APP_ENV").unwrap_or_else(|_| "local".into());
        let profile_path = format!("config/{}.toml", env_profile);

        Figment::new()
            // 1. Load base configuration
            .merge(Toml::file(config_path))
            // 2. Load environment-specific configuration (e.g., production.toml), which can override the base.
            .merge(Toml::file(profile_path).nested())
            // 3. Load secrets and overrides from environment variables.
            // e.g., APP_SECRETS_API_KEYS_BINANCE=...
            // e.g., APP_STRATEGY_KELLY_FRACTION_CAP=0.25
            .merge(Env::prefixed("APP_").split("__"))
            .extract()
    }

    /// Comprehensive validation for semantic correctness
    /// This goes beyond type safety to catch logical and business rule violations
    pub fn validate(&self) -> Result<()> {
        // Strategy validation
        self.validate_strategy_config()?;

        // Execution validation
        self.validate_execution_config()?;

        // Chain validation
        self.validate_chains_config()?;

        // Cross-field validation
        self.validate_cross_field_consistency()?;

        // Business logic validation
        self.validate_business_rules()?;

        Ok(())
    }

    /// Validate strategy configuration parameters
    fn validate_strategy_config(&self) -> Result<()> {
        // Kelly fraction cap validation (critical for risk management)
        if self.strategy.kelly_fraction_cap <= 0.0 || self.strategy.kelly_fraction_cap > 1.0 {
            anyhow::bail!(
                "strategy.kelly_fraction_cap must be between 0.0 and 1.0, got: {}. \
                Kelly criterion requires fraction ≤ 1.0 for mathematical validity.",
                self.strategy.kelly_fraction_cap
            );
        }

        // Warn if Kelly fraction is too aggressive
        if self.strategy.kelly_fraction_cap > 0.5 {
            tracing::warn!(
                "Kelly fraction cap {} is very aggressive. Consider values ≤ 0.25 for safer trading.",
                self.strategy.kelly_fraction_cap
            );
        }

        // Minimum profitability validation
        if self.strategy.min_profitability_bps == 0 {
            anyhow::bail!("strategy.min_profitability_bps must be positive to ensure profitable trades");
        }

        if self.strategy.min_profitability_bps > 10000 {
            anyhow::bail!(
                "strategy.min_profitability_bps cannot exceed 10000 (100%), got: {}",
                self.strategy.min_profitability_bps
            );
        }

        // Strategy enablement validation
        if self.strategy.enabled_strategies.is_empty() {
            anyhow::bail!("At least one strategy must be enabled");
        }

        // Validate strategy names
        let valid_strategies = ["zen_geometer", "basilisk_gaze", "pilot_fish", "aetheric_resonance"];
        for strategy in &self.strategy.enabled_strategies {
            if !valid_strategies.contains(&strategy.as_str()) {
                anyhow::bail!(
                    "Unknown strategy '{}'. Valid strategies: {:?}",
                    strategy, valid_strategies
                );
            }
        }

        Ok(())
    }

    /// Validate execution configuration parameters
    fn validate_execution_config(&self) -> Result<()> {
        // Slippage validation (0.01% to 10% is reasonable)
        if self.execution.max_slippage_bps == 0 {
            anyhow::bail!("execution.max_slippage_bps must be positive");
        }

        if self.execution.max_slippage_bps > 1000 {
            anyhow::bail!(
                "execution.max_slippage_bps cannot exceed 1000 (10%), got: {}. \
                High slippage increases MEV vulnerability.",
                self.execution.max_slippage_bps
            );
        }

        // Warn for high slippage
        if self.execution.max_slippage_bps > 500 {
            tracing::warn!(
                "High slippage tolerance: {}bps ({}%). Consider lower values to reduce MEV risk.",
                self.execution.max_slippage_bps,
                self.execution.max_slippage_bps as f64 / 100.0
            );
        }

        // Gas limit multiplier validation (1.0x to 3.0x is reasonable)
        if self.execution.gas_limit_multiplier < 1.0 {
            anyhow::bail!(
                "execution.gas_limit_multiplier must be ≥ 1.0, got: {}",
                self.execution.gas_limit_multiplier
            );
        }

        if self.execution.gas_limit_multiplier > 3.0 {
            anyhow::bail!(
                "execution.gas_limit_multiplier cannot exceed 3.0, got: {}. \
                Excessive gas limits waste funds.",
                self.execution.gas_limit_multiplier
            );
        }

        // Gas price validation (reasonable bounds for mainnet)
        if self.execution.max_gas_price_gwei == 0 {
            anyhow::bail!("execution.max_gas_price_gwei must be positive");
        }

        if self.execution.max_gas_price_gwei > 1000 {
            anyhow::bail!(
                "execution.max_gas_price_gwei cannot exceed 1000 gwei, got: {}. \
                Extremely high gas prices are likely configuration errors.",
                self.execution.max_gas_price_gwei
            );
        }

        // Warn for very high gas prices
        if self.execution.max_gas_price_gwei > 200 {
            tracing::warn!(
                "High max gas price: {} gwei. Ensure this is intentional for your network.",
                self.execution.max_gas_price_gwei
            );
        }

        Ok(())
    }

    /// Validate chain configurations
    fn validate_chains_config(&self) -> Result<()> {
        if self.chains.is_empty() {
            anyhow::bail!("At least one chain must be configured");
        }

        for (chain_id, chain_config) in &self.chains {
            // Chain ID validation
            if *chain_id == 0 {
                anyhow::bail!("Chain ID cannot be zero");
            }

            // Validate known chain IDs
            let known_chains = [
                (1, "Ethereum Mainnet"),
                (8453, "Base"),
                (137, "Polygon"),
                (42161, "Arbitrum One"),
                (10, "Optimism"),
                (56, "BSC"),
                (43114, "Avalanche"),
            ];

            let is_known_chain = known_chains.iter().any(|(id, _)| id == chain_id);
            if !is_known_chain {
                tracing::warn!(
                    "Unknown chain ID: {}. Ensure this is correct for chain '{}'",
                    chain_id, chain_config.name
                );
            }

            // RPC URL validation
            if chain_config.rpc_url.is_empty() {
                anyhow::bail!("Chain {} has empty rpc_url", chain_id);
            }

            // Basic URL format validation
            if !chain_config.rpc_url.starts_with("http://") &&
               !chain_config.rpc_url.starts_with("https://") &&
               !chain_config.rpc_url.starts_with("wss://") &&
               !chain_config.rpc_url.starts_with("ws://") {
                anyhow::bail!(
                    "Chain {} RPC URL must start with http://, https://, ws://, or wss://, got: {}",
                    chain_id, chain_config.rpc_url
                );
            }

            // Gas price validation
            if chain_config.max_gas_price == 0 {
                anyhow::bail!("Chain {} max_gas_price must be positive", chain_id);
            }

            // Private key environment variable validation
            if chain_config.private_key_env_var.is_empty() {
                anyhow::bail!("Chain {} private_key_env_var cannot be empty", chain_id);
            }

            // Validate environment variable naming convention
            if !chain_config.private_key_env_var.ends_with("_PRIVATE_KEY") {
                tracing::warn!(
                    "Chain {} private key env var '{}' doesn't follow *_PRIVATE_KEY convention",
                    chain_id, chain_config.private_key_env_var
                );
            }

            // Contract address validation (if provided)
            if let Some(stargate) = &chain_config.contracts.stargate_compass_v1 {
                if !Self::is_valid_ethereum_address(stargate) {
                    anyhow::bail!(
                        "Chain {} stargate_compass_v1 address is invalid: {}",
                        chain_id, stargate
                    );
                }
            }

            if let Some(multicall) = &chain_config.contracts.multicall {
                if !Self::is_valid_ethereum_address(multicall) {
                    anyhow::bail!(
                        "Chain {} multicall address is invalid: {}",
                        chain_id, multicall
                    );
                }
            }
        }

        Ok(())
    }

    /// Validate cross-field consistency
    fn validate_cross_field_consistency(&self) -> Result<()> {
        // Ensure strategy profitability is achievable given execution costs
        let min_profit_usd = (self.strategy.min_profitability_bps as f64) / 10000.0 * 100.0; // Rough estimate
        let max_slippage_cost = (self.execution.max_slippage_bps as f64) / 10000.0 * 100.0;

        if min_profit_usd <= max_slippage_cost * 2.0 {
            tracing::warn!(
                "Minimum profitability ({}bps) may be too low relative to max slippage ({}bps). \
                Consider higher profitability targets to account for execution costs.",
                self.strategy.min_profitability_bps, self.execution.max_slippage_bps
            );
        }

        // Validate Kelly fraction vs enabled strategies
        if self.strategy.enabled_strategies.len() > 3 && self.strategy.kelly_fraction_cap > 0.3 {
            tracing::warn!(
                "Running {} strategies with Kelly fraction {} may be too aggressive. \
                Consider lower Kelly fraction for multi-strategy setups.",
                self.strategy.enabled_strategies.len(), self.strategy.kelly_fraction_cap
            );
        }

        Ok(())
    }

    /// Validate business rules and trading logic
    fn validate_business_rules(&self) -> Result<()> {
        // Environment-specific validations
        let env = std::env::var("APP_ENV").unwrap_or_else(|_| "local".into());

        match env.as_str() {
            "production" => {
                // Production-specific validations
                if self.strategy.kelly_fraction_cap > 0.5 {
                    anyhow::bail!(
                        "Kelly fraction cap {} is too aggressive for production. Maximum 0.5 allowed.",
                        self.strategy.kelly_fraction_cap
                    );
                }

                if self.execution.max_slippage_bps > 300 {
                    anyhow::bail!(
                        "Slippage tolerance {}bps is too high for production. Maximum 300bps allowed.",
                        self.execution.max_slippage_bps
                    );
                }

                // Ensure all chains have reasonable gas limits for production
                for (chain_id, _) in &self.chains {
                    if *chain_id == 1 && self.execution.max_gas_price_gwei > 100 {
                        tracing::warn!(
                            "High gas price limit for Ethereum mainnet in production: {} gwei",
                            self.execution.max_gas_price_gwei
                        );
                    }
                }
            }
            "staging" => {
                // Staging-specific validations
                if self.strategy.kelly_fraction_cap > 0.75 {
                    tracing::warn!(
                        "Kelly fraction cap {} is high for staging environment",
                        self.strategy.kelly_fraction_cap
                    );
                }
            }
            _ => {
                // Development/local validations are more relaxed
                if self.strategy.kelly_fraction_cap > 0.9 {
                    tracing::warn!(
                        "Kelly fraction cap {} is extremely high even for development",
                        self.strategy.kelly_fraction_cap
                    );
                }
            }
        }

        Ok(())
    }

    /// Validate Ethereum address format
    fn is_valid_ethereum_address(address: &str) -> bool {
        // Basic validation: starts with 0x and is 42 characters long
        address.starts_with("0x") &&
        address.len() == 42 &&
        address[2..].chars().all(|c| c.is_ascii_hexdigit())
    }

    /// Comprehensive adapter to convert new Config to old Settings for gradual migration
    /// This allows us to use the new loading system while keeping existing code working
    pub fn to_settings(&self) -> Settings {
        let mut settings = Settings::default();

        // Map basic app config
        settings.app.name = self.app_name.clone();
        settings.app.environment = std::env::var("APP_ENV").unwrap_or_else(|_| "local".into());
        settings.app.version = "0.1.0".to_string(); // Default version

        // Map strategy config to risk config (kelly_fraction_cap)
        settings.risk.kelly_fraction_cap = Decimal::try_from(self.strategy.kelly_fraction_cap).unwrap_or_default();

        // Map execution config
        settings.execution.max_slippage_bps = self.execution.max_slippage_bps;
        settings.execution.gas_limit_multiplier = Decimal::try_from(self.execution.gas_limit_multiplier).unwrap_or_default();
        settings.execution.max_gas_price_gwei = self.execution.max_gas_price_gwei;

        // Map strategies config
        settings.strategies.enabled_strategies = self.strategy.enabled_strategies.clone();

        // Set reasonable defaults for missing fields
        settings.dry_run = std::env::var("DRY_RUN").unwrap_or_else(|_| "true".into()) == "true";
        settings.active_chain_id = self.chains.keys().next().copied().unwrap_or(8453);

        // Map chains - convert from new format to old format
        for (chain_id, chain_config) in &self.chains {
            let chain_id_str = chain_id.to_string();
            let mut old_chain_config = crate::config::ChainConfig::default();
            old_chain_config.name = chain_config.name.clone();
            old_chain_config.chain_id = *chain_id;

            // Map RPC endpoints
            old_chain_config.rpc_endpoints = vec![crate::config::RpcEndpoint {
                url: chain_config.rpc_url.clone(),
                priority: 0,
                timeout_ms: 30000,
            }];

            settings.chains.insert(chain_id_str, old_chain_config);
        }

        // Set default values for other required fields
        settings.database.url = std::env::var("DATABASE_URL")
            .unwrap_or_else(|_| "postgres://localhost:5432/basilisk".into());
        settings.redis.url = std::env::var("REDIS_URL")
            .unwrap_or_else(|_| "redis://localhost:6379".into());
        settings.nats.url = std::env::var("NATS_URL")
            .unwrap_or_else(|_| "nats://localhost:4222".into());

        settings
    }

    /// Load configuration using the new elegant system and convert to old format
    /// This is the bridge function for gradual migration
    pub fn load_as_settings() -> Result<Settings, Box<dyn std::error::Error>> {
        let new_config = Self::load()
            .map_err(|e| format!("Failed to load new configuration: {}", e))?;

        new_config.validate()
            .map_err(|e| format!("Configuration validation failed: {}", e))?;

        Ok(new_config.to_settings())
    }
}

#[cfg(test)]
mod elegant_config_tests {
    use super::*;
    use std::env;

    #[test]
    fn test_new_config_loading() {
        // Set environment variables for testing
        env::set_var("CONFIG_PATH", "config/elegant-minimal.toml");
        env::set_var("APP_ENV", "test");

        // Test loading the new configuration
        let result = Config::load();

        match result {
            Ok(config) => {
                assert_eq!(config.app_name, "basilisk_bot");
                assert_eq!(config.log_level, "info");
                assert_eq!(config.strategy.kelly_fraction_cap, 0.25);
                assert!(config.chains.contains_key(&8453));

                // Test validation
                assert!(config.validate().is_ok());

                println!("✅ New configuration system loaded successfully!");
                println!("Chains configured: {:?}", config.chains.keys().collect::<Vec<_>>());
            }
            Err(e) => {
                println!("❌ Failed to load configuration: {}", e);
                // Don't panic in test, just print the error for debugging
            }
        }
    }

    #[test]
    fn test_config_adapter() {
        // Test the adapter function that converts new Config to old Settings
        env::set_var("CONFIG_PATH", "config/elegant-minimal.toml");
        env::set_var("APP_ENV", "test");

        match Config::load_as_settings() {
            Ok(settings) => {
                println!("✅ Adapter function works!");
                println!("App name: {}", settings.app.name);
                println!("Kelly fraction cap: {}", settings.risk.kelly_fraction_cap);
                println!("Chains: {}", settings.chains.len());

                // Verify the conversion worked
                assert_eq!(settings.app.name, "basilisk_bot");
                assert!(settings.chains.contains_key("8453"));
            }
            Err(e) => {
                println!("❌ Adapter function failed: {}", e);
            }
        }
    }

    #[test]
    fn test_enhanced_validation() {
        use std::env;

        // Test valid configuration
        env::set_var("CONFIG_PATH", "config/elegant-minimal.toml");
        env::set_var("APP_ENV", "test");

        match Config::load() {
            Ok(config) => {
                println!("✅ Configuration loaded for validation test");

                // Test comprehensive validation
                match config.validate() {
                    Ok(()) => {
                        println!("✅ Enhanced validation passed!");
                    }
                    Err(e) => {
                        println!("❌ Validation failed: {}", e);
                        // Don't panic in test, just report
                    }
                }
            }
            Err(e) => {
                println!("❌ Failed to load config for validation test: {}", e);
            }
        }
    }

    #[test]
    fn test_validation_edge_cases() {
        // Test invalid Kelly fraction
        let mut config = Config {
            app_name: "test".to_string(),
            log_level: "info".to_string(),
            chains: std::collections::HashMap::new(),
            strategy: StrategyConfig {
                kelly_fraction_cap: 1.5, // Invalid: > 1.0
                min_profitability_bps: 50,
                enabled_strategies: vec!["zen_geometer".to_string()],
            },
            execution: ExecutionConfig {
                max_slippage_bps: 500,
                gas_limit_multiplier: 1.2,
                max_gas_price_gwei: 100,
            },
            secrets: Secrets {
                api_keys: std::collections::HashMap::new(),
                private_keys: std::collections::HashMap::new(),
            },
        };

        // Should fail validation
        assert!(config.validate().is_err());
        println!("✅ Invalid Kelly fraction correctly rejected");

        // Fix Kelly fraction, test invalid slippage
        config.strategy.kelly_fraction_cap = 0.25;
        config.execution.max_slippage_bps = 2000; // Invalid: > 1000

        assert!(config.validate().is_err());
        println!("✅ Invalid slippage correctly rejected");

        // Fix slippage, test empty chains
        config.execution.max_slippage_bps = 500;
        // chains is already empty

        assert!(config.validate().is_err());
        println!("✅ Empty chains correctly rejected");
    }
}

// ============================================================================
// NEW ELEGANT CONFIGURATION SYSTEM (Following the Guide)
// ============================================================================

/// New canonical configuration structure following the elegant configuration guide
/// This will replace Settings once migration is complete
#[derive(Debug, Deserialize, Clone)]
pub struct Config {
    pub app_name: String,
    pub log_level: String,
    #[serde(default)]
    pub chains: HashMap<u64, ChainConfig>,
    pub strategy: StrategyConfig,
    pub execution: ExecutionConfig,
    pub secrets: Secrets,
}

/// Chain-specific configuration
#[derive(Debug, Deserialize, Clone)]
pub struct ChainConfig {
    pub name: String,
    pub rpc_url: String,
    pub max_gas_price: u64,
    pub private_key_env_var: String, // Reference to the env var
    pub contracts: ContractAddresses,
    pub dex: DexConfig,
}

/// Contract addresses for each chain
#[derive(Debug, Deserialize, Clone)]
pub struct ContractAddresses {
    pub stargate_compass_v1: Option<String>,
    pub multicall: Option<String>,
    // Add other contract addresses here
}

/// DEX configuration for each chain
#[derive(Debug, Deserialize, Clone)]
pub struct DexConfig {
    pub degen_swap_router: Option<String>,
    pub uniswap_v2_router: Option<String>,
    // Add other DEX-related configs
}

/// Strategy configuration
#[derive(Debug, Deserialize, Clone)]
pub struct StrategyConfig {
    pub kelly_fraction_cap: f64,
    pub min_profitability_bps: u64,
    pub enabled_strategies: Vec<String>,
    // ... other strategy parameters
}

/// Execution configuration
#[derive(Debug, Deserialize, Clone)]
pub struct ExecutionConfig {
    pub max_slippage_bps: u64,
    pub gas_limit_multiplier: f64,
    pub max_gas_price_gwei: u64,
    // ... other execution parameters
}

/// Secrets loaded from environment variables
#[derive(Debug, Deserialize, Clone)]
pub struct Secrets {
    // This struct will be populated from environment variables
    #[serde(default)]
    pub api_keys: HashMap<String, String>,
    #[serde(default)]
    pub private_keys: HashMap<String, String>,
}

impl Config {
    /// Load configuration using figment with layered approach
    pub fn load() -> Result<Self, figment::Error> {
        let config_path = std::env::var("CONFIG_PATH").unwrap_or_else(|_| "config/default.toml".into());
        let env_profile = std::env::var("APP_ENV").unwrap_or_else(|_| "local".into());
        let profile_path = format!("config/{}.toml", env_profile);

        Figment::new()
            // 1. Load base configuration
            .merge(Toml::file(config_path))
            // 2. Load environment-specific configuration (e.g., production.toml), which can override the base.
            .merge(Toml::file(profile_path).nested())
            // 3. Load secrets and overrides from environment variables.
            // e.g., APP_SECRETS_API_KEYS_BINANCE=...
            // e.g., APP_STRATEGY_KELLY_FRACTION_CAP=0.25
            .merge(Env::prefixed("APP_").split("__"))
            .extract()
    }

    /// Validate configuration for semantic correctness
    pub fn validate(&self) -> Result<()> {
        if self.strategy.kelly_fraction_cap <= 0.0 || self.strategy.kelly_fraction_cap > 1.0 {
            anyhow::bail!("strategy.kelly_fraction_cap must be between 0.0 and 1.0");
        }

        for (chain_id, chain_config) in &self.chains {
            if chain_config.rpc_url.is_empty() {
                anyhow::bail!("chain (id={}) has an empty rpc_url", chain_id);
            }
        }

        // Add more checks here...

        Ok(())
    }
}