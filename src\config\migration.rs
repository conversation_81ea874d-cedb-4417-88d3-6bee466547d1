// Configuration Migration Utilities
// Helps track and manage the gradual migration from Settings to Config

use std::collections::HashSet;
use tracing::{info, warn};

/// Migration tracker to monitor progress and identify remaining work
pub struct MigrationTracker {
    migrated_modules: HashSet<String>,
    pending_modules: HashSet<String>,
}

impl MigrationTracker {
    pub fn new() -> Self {
        let mut pending_modules = HashSet::new();
        
        // List of modules that need migration (based on codebase analysis)
        pending_modules.insert("strategies::scanners::gaze".to_string());
        pending_modules.insert("strategies::nomadic_hunter".to_string());
        pending_modules.insert("config::wizard".to_string());
        pending_modules.insert("config::cli_handlers".to_string());
        pending_modules.insert("execution::manager".to_string());
        pending_modules.insert("strategies::manager".to_string());
        pending_modules.insert("risk::manager".to_string());
        pending_modules.insert("data::pipeline".to_string());
        pending_modules.insert("network::client".to_string());
        pending_modules.insert("tui::interface".to_string());
        
        Self {
            migrated_modules: HashSet::new(),
            pending_modules,
        }
    }
    
    /// Mark a module as migrated to the new Config system
    pub fn mark_migrated(&mut self, module: &str) {
        self.pending_modules.remove(module);
        self.migrated_modules.insert(module.to_string());
        info!("✅ Module '{}' migrated to new Config system", module);
    }
    
    /// Get migration progress
    pub fn get_progress(&self) -> (usize, usize) {
        (self.migrated_modules.len(), self.migrated_modules.len() + self.pending_modules.len())
    }
    
    /// Print migration status
    pub fn print_status(&self) {
        let (migrated, total) = self.get_progress();
        let percentage = if total > 0 { (migrated * 100) / total } else { 0 };
        
        info!("🔄 Configuration Migration Progress: {}/{} ({}%)", migrated, total, percentage);
        
        if !self.pending_modules.is_empty() {
            warn!("📋 Pending modules:");
            for module in &self.pending_modules {
                warn!("  - {}", module);
            }
        }
        
        if migrated == total {
            info!("🎉 Migration complete! All modules using new Config system.");
        }
    }
}

/// Helper functions for gradual migration
pub mod helpers {
    use super::*;
    use crate::config::{Config, Settings};
    use std::sync::Arc;
    
    /// Create a bridge function that accepts either old or new config
    /// This allows components to gradually migrate
    pub trait ConfigBridge {
        fn from_old_settings(settings: Arc<Settings>) -> Self;
        fn from_new_config(config: Arc<Config>) -> Self;
    }
    
    /// Utility to get kelly fraction from either config system
    pub fn get_kelly_fraction(settings: &Settings) -> f64 {
        // Convert Decimal to f64 for compatibility
        settings.risk.kelly_fraction_cap.to_string().parse().unwrap_or(0.25)
    }
    
    /// Utility to get slippage from either config system
    pub fn get_max_slippage_bps(settings: &Settings) -> u64 {
        settings.execution.max_slippage_bps
    }
    
    /// Utility to get chain config by ID
    pub fn get_chain_config(settings: &Settings, chain_id: u64) -> Option<&crate::config::ChainConfig> {
        settings.chains.get(&chain_id.to_string())
    }
    
    /// Check if we're using the new config system
    pub fn is_using_new_config() -> bool {
        std::env::var("USE_NEW_CONFIG").unwrap_or_else(|_| "false".into()) == "true"
    }
    
    /// Log migration status for a component
    pub fn log_component_migration(component: &str, using_new_config: bool) {
        if using_new_config {
            info!("🆕 Component '{}' using new Config system", component);
        } else {
            info!("🔄 Component '{}' using legacy Settings (migration pending)", component);
        }
    }
}

/// Migration phases for systematic rollout
#[derive(Debug, Clone, Copy)]
pub enum MigrationPhase {
    /// Phase 1: Core infrastructure (config loading, validation)
    CoreInfrastructure,
    /// Phase 2: Strategy components (scanners, managers)
    StrategyComponents,
    /// Phase 3: Execution and risk management
    ExecutionAndRisk,
    /// Phase 4: UI and CLI components
    UserInterface,
    /// Phase 5: Final cleanup and old system removal
    Cleanup,
}

impl MigrationPhase {
    pub fn description(&self) -> &'static str {
        match self {
            MigrationPhase::CoreInfrastructure => "Core configuration loading and validation",
            MigrationPhase::StrategyComponents => "Strategy scanners and managers",
            MigrationPhase::ExecutionAndRisk => "Execution engine and risk management",
            MigrationPhase::UserInterface => "TUI and CLI interfaces",
            MigrationPhase::Cleanup => "Remove old Settings system",
        }
    }
    
    pub fn modules(&self) -> Vec<&'static str> {
        match self {
            MigrationPhase::CoreInfrastructure => vec!["config", "main"],
            MigrationPhase::StrategyComponents => vec!["strategies::scanners", "strategies::manager"],
            MigrationPhase::ExecutionAndRisk => vec!["execution::manager", "risk::manager"],
            MigrationPhase::UserInterface => vec!["tui", "cli_handlers"],
            MigrationPhase::Cleanup => vec!["remove_old_settings"],
        }
    }
}

/// Migration plan executor
pub struct MigrationPlan {
    current_phase: MigrationPhase,
    tracker: MigrationTracker,
}

impl MigrationPlan {
    pub fn new() -> Self {
        Self {
            current_phase: MigrationPhase::CoreInfrastructure,
            tracker: MigrationTracker::new(),
        }
    }
    
    pub fn execute_phase(&mut self, phase: MigrationPhase) {
        info!("🚀 Starting migration phase: {}", phase.description());
        
        for module in phase.modules() {
            info!("📦 Migrating module: {}", module);
            // In a real migration, this would trigger the actual migration
            self.tracker.mark_migrated(module);
        }
        
        self.current_phase = phase;
        self.tracker.print_status();
    }
}
