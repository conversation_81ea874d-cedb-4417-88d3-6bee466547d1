# Minimal configuration for testing the elegant configuration system

app_name = "basilisk_bot"
log_level = "info"

[strategy]
kelly_fraction_cap = 0.25
min_profitability_bps = 50
enabled_strategies = ["zen_geometer"]

[execution]
max_slippage_bps = 500
gas_limit_multiplier = 1.2
max_gas_price_gwei = 100

[secrets]
# Secrets loaded from environment variables

# Single chain for testing
[chains.8453]
name = "Base"
rpc_url = "https://mainnet.base.org"
max_gas_price = 50000000000
private_key_env_var = "BASE_PRIVATE_KEY"

[chains.8453.contracts]
multicall = "0xcA11bde05977b3631167028862bE2a173976CA11"

[chains.8453.dex]
uniswap_v2_router = "0x4752ba5dbc23f44d87826276bf6fd6b1c372ad24"
