# Test configuration for the new elegant configuration system

app_name = "basilisk_bot"
log_level = "info"

[strategy]
kelly_fraction_cap = 0.25
min_profitability_bps = 50
enabled_strategies = ["zen_geometer", "basilisk_gaze"]

[execution]
max_slippage_bps = 500
gas_limit_multiplier = 1.2
max_gas_price_gwei = 100

[secrets]
# Secrets will be loaded from environment variables

# Chain configurations
[chains.8453]
name = "Base"
rpc_url = "https://mainnet.base.org"
max_gas_price = 50000000000
private_key_env_var = "BASE_PRIVATE_KEY"

[chains.8453.contracts]
stargate_compass_v1 = "******************************************"
multicall = "******************************************"

[chains.8453.dex]
degen_swap_router = "******************************************"
uniswap_v2_router = "******************************************"

[chains.1]
name = "Ethereum"
rpc_url = "https://eth.llamarpc.com"
max_gas_price = 100000000000
private_key_env_var = "ETH_PRIVATE_KEY"

[chains.1.contracts]
multicall = "******************************************"

[chains.1.dex]
uniswap_v2_router = "******************************************"
