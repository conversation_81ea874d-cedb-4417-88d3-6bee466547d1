# Production Configuration for Elegant Configuration System
# This demonstrates the full capabilities of the new configuration system

app_name = "basilisk_bot"
log_level = "info"

[strategy]
# Conservative Kelly fraction for production
kelly_fraction_cap = 0.25
# Minimum profitability in basis points (0.5% = 50bps)
min_profitability_bps = 50
# Enabled strategies for production
enabled_strategies = ["zen_geometer", "basilisk_gaze"]

[execution]
# Maximum slippage tolerance in basis points (3% = 300bps)
max_slippage_bps = 300
# Gas limit multiplier for safety
gas_limit_multiplier = 1.2
# Maximum gas price in gwei
max_gas_price_gwei = 100

[secrets]
# Secrets are loaded from environment variables
# Use APP_SECRETS__API_KEYS__* and APP_SECRETS__PRIVATE_KEYS__*

# Ethereum Mainnet Configuration
[chains.1]
name = "Ethereum"
rpc_url = "https://eth.llamarpc.com"
max_gas_price = 100000000000  # 100 gwei
private_key_env_var = "ETH_PRIVATE_KEY"

[chains.1.contracts]
multicall = "******************************************"
stargate_compass_v1 = "******************************************"

[chains.1.dex]
uniswap_v2_router = "******************************************"

# Base Network Configuration
[chains.8453]
name = "Base"
rpc_url = "https://mainnet.base.org"
max_gas_price = 50000000000  # 50 gwei
private_key_env_var = "BASE_PRIVATE_KEY"

[chains.8453.contracts]
multicall = "******************************************"
stargate_compass_v1 = "******************************************"

[chains.8453.dex]
uniswap_v2_router = "******************************************"
degen_swap_router = "******************************************"

# Arbitrum One Configuration
[chains.42161]
name = "Arbitrum One"
rpc_url = "https://arb1.arbitrum.io/rpc"
max_gas_price = 10000000000  # 10 gwei
private_key_env_var = "ARB_PRIVATE_KEY"

[chains.42161.contracts]
multicall = "******************************************"
stargate_compass_v1 = "0x53Bf833A5d6c4ddA888F69c22C88C9f356a41614"

[chains.42161.dex]
uniswap_v2_router = "0x1b02dA8Cb0d097eB8D57A175b88c7D8b47997506"

# Polygon Configuration
[chains.137]
name = "Polygon"
rpc_url = "https://polygon-rpc.com"
max_gas_price = 500000000000  # 500 gwei
private_key_env_var = "POLYGON_PRIVATE_KEY"

[chains.137.contracts]
multicall = "******************************************"

[chains.137.dex]
uniswap_v2_router = "0xa5E0829CaCEd8fFDD4De3c43696c57F7D7A678ff"

# Optimism Configuration
[chains.10]
name = "Optimism"
rpc_url = "https://mainnet.optimism.io"
max_gas_price = 20000000000  # 20 gwei
private_key_env_var = "OP_PRIVATE_KEY"

[chains.10.contracts]
multicall = "******************************************"

[chains.10.dex]
uniswap_v2_router = "0x68b3465833fb72A70ecDF485E0e4C7bD8665Fc45"
