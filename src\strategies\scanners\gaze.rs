// GazeScanner: DEX-DEX Arbitrage Scanner for Degen Chain
// Scans for price discrepancies between different DEXes on Degen Chain
// that can be exploited using flash loans from Base

use anyhow::Result;
use ethers::{
    providers::{Http, Provider, Middleware},
    types::{Address, U256},
    contract::Contract,
    abi::Abi,
};
use rust_decimal::Decimal;
use rust_decimal_macros::dec;
use num_traits::ToPrimitive;
use std::str::FromStr;
use std::sync::Arc;
use std::collections::HashMap;
use tokio::time::{interval, Duration};
use tracing::{debug, error, info, warn};
use crate::error::BasiliskError;

use crate::shared_types::{SimpleOpportunity as Opportunity, OpportunityType, StrategyType};
use crate::strategies::honeypot_checker::{HoneypotChecker, SecurityStatus};
use crate::config::{Settings, Config, migration::helpers::ConfigBridge};

pub struct GazeScanner {
    provider: Arc<Provider<Http>>,
    honeypot_checker: Arc<HoneypotChecker>,
    config: Arc<Settings>,
    dex_routers: HashMap<String, Address>,
    min_profit_threshold: Decimal,
}

impl GazeScanner {
    /// Legacy constructor for backward compatibility during migration
    pub fn new(
        provider: Arc<Provider<Http>>,
        honeypot_checker: Arc<HoneypotChecker>,
        config: Arc<Settings>,
    ) -> Self {
        crate::config::migration::helpers::log_component_migration("GazeScanner", false);
        Self::from_old_settings(provider, honeypot_checker, config)
    }

    /// New constructor using the elegant Config system
    pub fn new_with_config(
        provider: Arc<Provider<Http>>,
        honeypot_checker: Arc<HoneypotChecker>,
        config: Arc<Config>,
    ) -> Self {
        crate::config::migration::helpers::log_component_migration("GazeScanner", true);
        Self::from_new_config(provider, honeypot_checker, config)
    }

    /// Common construction logic used by both config systems
    fn build(
        provider: Arc<Provider<Http>>,
        honeypot_checker: Arc<HoneypotChecker>,
        config: Arc<Settings>, // Keep Settings for now during transition
        dex_routers: HashMap<String, Address>,
        min_profit_threshold: Decimal,
    ) -> Self {
        Self {
            provider,
            honeypot_checker,
            config,
            dex_routers,
            min_profit_threshold,
        }
    }
}

/// Migration bridge implementation for GazeScanner
impl ConfigBridge for GazeScanner {
    fn from_old_settings(settings: Arc<Settings>) -> Self {
        // This is a placeholder - actual implementation would need the other parameters
        unimplemented!("Use the static methods with all parameters instead")
    }

    fn from_new_config(config: Arc<Config>) -> Self {
        // This is a placeholder - actual implementation would need the other parameters
        unimplemented!("Use the static methods with all parameters instead")
    }
}

impl GazeScanner {
    /// Create from old Settings system
    pub fn from_old_settings(
        provider: Arc<Provider<Http>>,
        honeypot_checker: Arc<HoneypotChecker>,
        settings: Arc<Settings>,
    ) -> Self {
        let mut dex_routers = HashMap::new();

        // Add DegenSwap router using old config structure
        if let Some(degen_chain) = settings.chains.get("666666666") {
            // Note: This assumes the old structure has dex field
            // You may need to adjust based on actual Settings structure
            dex_routers.insert("DegenSwap".to_string(), Address::zero()); // Placeholder
        }

        Self::build(
            provider,
            honeypot_checker,
            settings,
            dex_routers,
            dec!(10.0), // Default minimum profit
        )
    }

    /// Create from new Config system
    pub fn from_new_config(
        provider: Arc<Provider<Http>>,
        honeypot_checker: Arc<HoneypotChecker>,
        config: Arc<Config>,
    ) -> Self {
        let mut dex_routers = HashMap::new();

        // Add DEX routers using new config structure
        for (chain_id, chain_config) in &config.chains {
            if let Some(router) = &chain_config.dex.degen_swap_router {
                if let Ok(addr) = router.parse::<Address>() {
                    dex_routers.insert(format!("DegenSwap_{}", chain_id), addr);
                }
            }

            if let Some(router) = &chain_config.dex.uniswap_v2_router {
                if let Ok(addr) = router.parse::<Address>() {
                    dex_routers.insert(format!("UniswapV2_{}", chain_id), addr);
                }
            }
        }

        // Convert new config to old Settings for now (during transition)
        let settings = Arc::new(config.to_settings());

        Self::build(
            provider,
            honeypot_checker,
            settings,
            dex_routers,
            dec!(10.0), // Could be configurable in new system
        )
    }

    /// Start scanning for arbitrage opportunities
    pub async fn start_scanning(&self) -> Result<()> {
        info!("Starting GazeScanner for Degen Chain DEX arbitrage");
        
        let mut scan_interval = interval(Duration::from_secs(5)); // Scan every 5 seconds
        
        loop {
            scan_interval.tick().await;
            
            if let Err(e) = self.scan_for_opportunities().await {
                error!("Error during gaze scan: {}", e);
            }
        }
    }

    /// Scan for arbitrage opportunities between DEXes
    pub async fn scan_for_opportunities(&self) -> Result<Vec<Opportunity>> {
        let mut opportunities = Vec::new();
        
        // Get popular token pairs on Degen Chain
        let token_pairs = self.get_popular_token_pairs().await?;
        
        for (token_a, token_b) in token_pairs {
            // Check if tokens are safe
            if !self.are_tokens_safe(&token_a, &token_b).await? {
                continue;
            }
            
            // Get prices from different DEXes
            let prices = self.get_prices_across_dexes(&token_a, &token_b).await?;
            
            // Look for arbitrage opportunities
            if let Some(opportunity) = self.analyze_price_differences(&token_a, &token_b, prices).await? {
                opportunities.push(opportunity);
            }
        }
        
        debug!("Found {} arbitrage opportunities", opportunities.len());
        Ok(opportunities)
    }

    /// Get popular token pairs for scanning
    async fn get_popular_token_pairs(&self) -> Result<Vec<(Address, Address)>> {
        let mut pairs = Vec::new();
        
        // Get token addresses from config
        if let Some(degen_chain) = self.config.chains.get(&666666666) {
            let tokens = &degen_chain.tokens;
            let usdc = tokens.usdc.parse::<Address>().ok();
            let degen = tokens.degen.as_ref().and_then(|s| s.parse::<Address>().ok());
            let weth = tokens.weth.as_ref().and_then(|s| s.parse::<Address>().ok());
            
            // Add major pairs
            if let (Some(usdc), Some(degen)) = (usdc, degen) {
                pairs.push((usdc, degen));
            }
            if let (Some(usdc), Some(weth)) = (usdc, weth) {
                pairs.push((usdc, weth));
            }
            if let (Some(degen), Some(weth)) = (degen, weth) {
                pairs.push((degen, weth));
            }
        }
        
        Ok(pairs)
    }

    /// Check if both tokens are safe to trade
    async fn are_tokens_safe(&self, token_a: &Address, token_b: &Address) -> Result<bool> {
        let status_a = self.honeypot_checker.check_token(*token_a).await?;
        let status_b = self.honeypot_checker.check_token(*token_b).await?;
        
        Ok(status_a == SecurityStatus::Safe && status_b == SecurityStatus::Safe)
    }

    /// Get prices for a token pair across different DEXes
    async fn get_prices_across_dexes(
        &self,
        token_a: &Address,
        token_b: &Address,
    ) -> Result<HashMap<String, (Decimal, Decimal)>> {
        let mut prices = HashMap::new();
        
        for (dex_name, router_addr) in &self.dex_routers {
            if let Ok((price_a_to_b, price_b_to_a)) = self.get_dex_price(router_addr, token_a, token_b).await {
                prices.insert(dex_name.clone(), (price_a_to_b, price_b_to_a));
            }
        }
        
        Ok(prices)
    }

    /// Get price from a specific DEX
    async fn get_dex_price(
        &self,
        router_addr: &Address,
        token_a: &Address,
        token_b: &Address,
    ) -> Result<(Decimal, Decimal)> {
        // Uniswap V2 router ABI for getAmountsOut
        let router_abi: Abi = serde_json::from_str(r#"[
            {
                "inputs": [
                    {"internalType": "uint256", "name": "amountIn", "type": "uint256"},
                    {"internalType": "address[]", "name": "path", "type": "address[]"}
                ],
                "name": "getAmountsOut",
                "outputs": [{"internalType": "uint256[]", "name": "amounts", "type": "uint256[]"}],
                "stateMutability": "view",
                "type": "function"
            }
        ]"#)?;
        
        let router_contract = Contract::new(*router_addr, router_abi, self.provider.clone());
        let test_amount = U256::from(10).pow(18.into()); // 1 token (assuming 18 decimals)
        
        // Get price A -> B
        let path_a_to_b = vec![*token_a, *token_b];
        let amounts_out_a_to_b: Vec<U256> = router_contract
            .method("getAmountsOut", (test_amount, path_a_to_b))?
            .call()
            .await?;
        
        // Get price B -> A
        let path_b_to_a = vec![*token_b, *token_a];
        let amounts_out_b_to_a: Vec<U256> = router_contract
            .method("getAmountsOut", (test_amount, path_b_to_a))?
            .call()
            .await?;
        
        let price_a_to_b = if amounts_out_a_to_b.len() >= 2 {
            Decimal::from_str(&amounts_out_a_to_b[1].to_string())? / Decimal::from_str(&test_amount.to_string())?
        } else {
            dec!(0)
        };
        
        let price_b_to_a = if amounts_out_b_to_a.len() >= 2 {
            Decimal::from_str(&amounts_out_b_to_a[1].to_string())? / Decimal::from_str(&test_amount.to_string())?
        } else {
            dec!(0)
        };
        
        Ok((price_a_to_b, price_b_to_a))
    }

    /// Analyze price differences to find arbitrage opportunities
    async fn analyze_price_differences(
        &self,
        token_a: &Address,
        token_b: &Address,
        prices: HashMap<String, (Decimal, Decimal)>,
    ) -> Result<Option<Opportunity>> {
        if prices.len() < 2 {
            return Ok(None); // Need at least 2 DEXes for arbitrage
        }
        
        let mut best_opportunity: Option<Opportunity> = None;
        let mut max_profit = dec!(0);
        
        // Compare prices between all DEX pairs
        for (dex1_name, (price1_a_to_b, _)) in &prices {
            for (dex2_name, (price2_a_to_b, _)) in &prices {
                if dex1_name == dex2_name {
                    continue;
                }
                
                // Calculate potential profit
                let price_diff = (price1_a_to_b - price2_a_to_b).abs();
                let profit_percentage = price_diff / price2_a_to_b.min(price1_a_to_b);
                
                if profit_percentage > dec!(0.005) { // 0.5% minimum profit margin
                    let estimated_profit = price_diff * dec!(1000); // Estimate with $1000 trade
                    
                    if estimated_profit > max_profit && estimated_profit > self.min_profit_threshold {
                        max_profit = estimated_profit;
                        
                        // Determine buy/sell direction
                        let (buy_dex, sell_dex, token_in, token_out) = if price1_a_to_b < price2_a_to_b {
                            (dex1_name, dex2_name, *token_a, *token_b)
                        } else {
                            (dex2_name, dex1_name, *token_a, *token_b)
                        };
                        
                        best_opportunity = Some(Opportunity {
                            id: uuid::Uuid::new_v4().to_string(),
                            strategy_type: StrategyType::ZenGeometer,
                            opportunity_type: OpportunityType::Arbitrage,
                            chain_id: 666666666, // Degen Chain
                            token_in: token_in,
                            token_out: token_out,
                            amount_in: U256::from(1000) * U256::from(10).pow(18.into()), // 1000 tokens
                            amount_out: U256::from((1000.0 * (*price1_a_to_b).to_f64().unwrap_or(1.0)) as u64) * U256::from(10).pow(18.into()),
                            estimated_profit_usd: estimated_profit,
                            gas_cost_usd: self.estimate_gas_cost_usd().await.unwrap_or(dec!(5.0)),
                            confidence_score: dec!(0.8),
                            deadline: chrono::Utc::now().timestamp() as u64 + 300, // 5 minutes
                            loan_amount: dec!(1000.0), // Flash loan amount in Decimal
                            metadata: {
                                let mut metadata = HashMap::new();
                                metadata.insert("buy_dex".to_string(), buy_dex.to_string());
                                metadata.insert("sell_dex".to_string(), sell_dex.to_string());
                                metadata.insert("profit_percentage".to_string(), profit_percentage.to_string());
                                metadata.insert("cross_chain".to_string(), "true".to_string());
                                metadata
                            },
                            ..Default::default()
                        });
                    }
                }
            }
        }
        
        if let Some(ref opp) = best_opportunity {
            info!(
                "Found arbitrage opportunity: {} profit on {}/{} pair",
                opp.estimated_profit_usd,
                token_a,
                token_b
            );
        }
        
        Ok(best_opportunity)
    }

    /// Estimate gas cost in USD for cross-chain transactions
    async fn estimate_gas_cost_usd(&self) -> Result<Decimal> {
        // Base gas cost for Stargate cross-chain transaction (conservative estimate)
        let base_gas_limit = 300_000u64;
        
        // Get current gas price from provider
        let gas_price = self.provider.get_gas_price().await
            .unwrap_or(U256::from(20_000_000_000u64)); // 20 gwei fallback
        
        // Calculate total gas cost in wei
        let total_gas_wei = gas_price * U256::from(base_gas_limit);
        
        // Convert to ETH (divide by 1e18)
        let gas_cost_eth = Decimal::from_str(&total_gas_wei.to_string())
            .unwrap_or(dec!(0.006)) / dec!(1e18);
        
        // Assume ETH price of $2000 (in production, this would come from price oracle)
        let eth_price_usd = dec!(2000.0);
        let gas_cost_usd = gas_cost_eth * eth_price_usd;
        
        // Add Stargate fee estimate (typically $1-3)
        let stargate_fee_usd = dec!(2.0);
        
        Ok(gas_cost_usd + stargate_fee_usd)
    }

    /// PHASE 2 IMPLEMENTATION: Live data ingestion with MultiCall efficiency
    /// Fetch pool reserves from multiple DEXes using a single multicall
    async fn fetch_pool_reserves_multicall(&self, pair: &TokenPair) -> Result<Vec<PoolReserves>> {
        // This would use the multicall pattern to efficiently fetch reserves
        // For now, simulate with individual calls for demonstration
        let mut reserves = Vec::new();
        
        for (dex_name, router_addr) in &self.dex_routers {
            if let Ok((reserve_a, reserve_b)) = self.get_pool_reserves(router_addr, &pair.token_address, &pair.base_token).await {
                reserves.push(PoolReserves {
                    pool_address: *router_addr, // Simplified - would be actual pool address
                    dex_name: dex_name.clone(),
                    token_a: pair.token_address,
                    token_b: pair.base_token,
                    reserve_a,
                    reserve_b,
                });
            }
        }
        
        Ok(reserves)
    }

    /// Get reserves for a specific pool
    async fn get_pool_reserves(&self, router_addr: &Address, token_a: &Address, token_b: &Address) -> Result<(U256, U256)> {
        // In production, this would call the pair contract directly
        // For now, use getAmountsOut as a proxy for liquidity
        let (price_a_to_b, _) = self.get_dex_price(router_addr, token_a, token_b).await?;
        
        // Simulate reserves based on price (placeholder)
        let reserve_a = U256::from(1000000) * U256::from(10).pow(18.into()); // 1M tokens
        let reserve_b = U256::from((1000000.0 * price_a_to_b.to_f64().unwrap_or(1.0)) as u64) * U256::from(10).pow(18.into());
        
        Ok((reserve_a, reserve_b))
    }

    /// Calculate price deviation between pools
    fn calculate_price_deviation(&self, reserves: &[PoolReserves]) -> Result<Decimal> {
        if reserves.len() < 2 {
            return Ok(dec!(0));
        }
        
        let price_1 = Decimal::from_str(&reserves[0].reserve_b.to_string())? / Decimal::from_str(&reserves[0].reserve_a.to_string())?;
        let price_2 = Decimal::from_str(&reserves[1].reserve_b.to_string())? / Decimal::from_str(&reserves[1].reserve_a.to_string())?;
        
        let deviation = (price_1 - price_2) / price_2;
        Ok(deviation)
    }

    /// Calculate gross profit from arbitrage
    fn calculate_gross_profit(&self, reserves: &[PoolReserves], price_deviation: Decimal) -> Result<Decimal> {
        // Simplified calculation - in production would use AMM formulas
        let trade_size = dec!(1000.0); // $1000 trade
        let gross_profit = trade_size * price_deviation.abs();
        Ok(gross_profit)
    }

    /// Estimate gas cost for arbitrage transaction
    async fn estimate_arbitrage_gas_cost(&self) -> Result<Decimal> {
        // Cross-chain arbitrage gas cost estimate
        self.estimate_gas_cost_usd().await
    }

    /// Calculate volatility for a token pair
    async fn calculate_volatility(&self, pair: &TokenPair) -> Result<Decimal> {
        // Placeholder - in production would analyze price history
        Ok(dec!(0.15)) // 15% volatility estimate
    }

    /// Calculate optimal trade size based on pool liquidity
    fn calculate_optimal_trade_size(&self, reserves: &[PoolReserves]) -> Result<U256> {
        // Use 1% of smallest pool's liquidity as max trade size
        let min_reserve = reserves.iter()
            .map(|r| r.reserve_a.min(r.reserve_b))
            .min()
            .unwrap_or(U256::from(1000000));
        
        Ok(min_reserve / U256::from(100)) // 1% of liquidity
    }

    /// Calculate expected output amount
    fn calculate_expected_output(&self, reserves: &[PoolReserves], price_deviation: Decimal) -> Result<U256> {
        // Simplified calculation
        let input_amount = self.calculate_optimal_trade_size(reserves)?;
        let output_multiplier = dec!(1.0) + price_deviation.abs();
        let expected_output = Decimal::from_str(&input_amount.to_string())? * output_multiplier;
        
        Ok(U256::from_dec_str(&expected_output.to_string()).unwrap_or(input_amount))
    }

    /// Calculate confidence score based on opportunity quality
    fn calculate_confidence_score(&self, price_deviation: Decimal, reserves: &[PoolReserves]) -> Decimal {
        let deviation_score = (price_deviation.abs() * dec!(100)).min(dec!(1.0)); // Cap at 100%
        let liquidity_score = if reserves.len() >= 2 { dec!(0.8) } else { dec!(0.5) };
        
        (deviation_score + liquidity_score) / dec!(2.0)
    }

    /// Build metadata for opportunity
    fn build_opportunity_metadata(&self, reserves: &[PoolReserves], price_deviation: Decimal) -> HashMap<String, String> {
        let mut metadata = HashMap::new();
        metadata.insert("price_deviation_percent".to_string(), (price_deviation * dec!(100)).to_string());
        metadata.insert("pools_analyzed".to_string(), reserves.len().to_string());
        metadata.insert("scanner_type".to_string(), "live_gaze".to_string());
        
        if reserves.len() >= 2 {
            metadata.insert("dex_1".to_string(), reserves[0].dex_name.clone());
            metadata.insert("dex_2".to_string(), reserves[1].dex_name.clone());
        }
        
        metadata
    }
}

/// Helper struct for pool reserve data
#[derive(Debug, Clone)]
struct PoolReserves {
    pool_address: Address,
    dex_name: String,
    token_a: Address,
    token_b: Address,
    reserve_a: U256,
    reserve_b: U256,
}

/// Helper struct for token pair data
#[derive(Debug, Clone)]
struct TokenPair {
    token_address: Address,
    base_token: Address,
}

// Export the scan function for compatibility
pub async fn scan() -> Result<Vec<Opportunity>> {
    // This is a fallback function - in practice, this would be called from the StrategyManager
    // with proper initialization. For now, return empty results.
    warn!("GazeScanner::scan called without proper initialization - use StrategyManager instead");
    Ok(vec![])
}